import React, { useState, useEffect } from 'react';
import { StyleSheet, StatusBar } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Pressable } from '@/components/ui/pressable';
import { Box } from '@/components/ui/box';
import { Text } from '@/components/ui/text';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Guideline } from './app/(tabs)/bones/Guideline';

type GuidelineState = 'scanning' | 'captured' | 'processing';

export default function TestGuidelineStates() {
  const insets = useSafeAreaInsets();
  const [currentState, setCurrentState] = useState<GuidelineState>('scanning');
  
  // Auto-cycle through states every 5 seconds for testing
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentState(prev => {
        if (prev === 'scanning') return 'captured';
        if (prev === 'captured') return 'processing';
        return 'scanning';
      });
    }, 5000);
    
    return () => clearInterval(interval);
  }, []);

  const handleReviewPress = () => {
    console.log('Review pressed');
  };

  const handleRetakePress = () => {
    console.log('Retake pressed');
    setCurrentState('scanning');
  };

  const customTips = [
    'Use good lighting for clear document photos',
    'Ensure documents are flat and fully visible',
    'Multiple files can be selected and processed together',
    'Tap "Photo Library" or "Camera" to add files'
  ];

  return (
    <LinearGradient
      colors={['#7c2d92', '#be185d', '#ec4899', '#f472b6']}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
      style={{ flex: 1 }}
    >
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />
      
      {/* Header */}
      <Box 
        className="flex-row justify-center items-center px-5 py-4 bg-white/10 backdrop-blur-md border-b border-white/20"
        style={{ paddingTop: insets.top + 16 }}
      >
        <Text className="text-white text-lg font-bold">
          Testing Guideline States
        </Text>
      </Box>

      {/* Current State Display */}
      <VStack className="flex-1 justify-center px-5 py-6">
        <Box className="bg-white/10 rounded-xl p-4 mb-6 backdrop-blur-md">
          <Text className="text-white text-xl font-bold text-center mb-4">
            Current State: {currentState.toUpperCase()}
          </Text>
          
          {/* Manual state controls */}
          <HStack className="gap-3">
            <Pressable 
              onPress={() => setCurrentState('scanning')}
              className={`flex-1 py-3 px-4 rounded-xl ${currentState === 'scanning' ? 'bg-pink-500' : 'bg-white/20'}`}
            >
              <Text className="text-white text-center font-medium">Scanning</Text>
            </Pressable>
            
            <Pressable 
              onPress={() => setCurrentState('captured')}
              className={`flex-1 py-3 px-4 rounded-xl ${currentState === 'captured' ? 'bg-pink-500' : 'bg-white/20'}`}
            >
              <Text className="text-white text-center font-medium">Captured</Text>
            </Pressable>
            
            <Pressable 
              onPress={() => setCurrentState('processing')}
              className={`flex-1 py-3 px-4 rounded-xl ${currentState === 'processing' ? 'bg-pink-500' : 'bg-white/20'}`}
            >
              <Text className="text-white text-center font-medium">Processing</Text>
            </Pressable>
          </HStack>
        </Box>

        {/* Large spacer to push guideline to bottom */}
        <Box className="flex-1" />
      </VStack>

      {/* Test Guideline Component */}
      <Guideline
        state={currentState}
        bottomInset={insets.bottom}
        marginBottom={20}
        onReviewPress={handleReviewPress}
        onRetakePress={handleRetakePress}
        customTips={customTips}
        initialExpanded={true}
      />
    </LinearGradient>
  );
}
