import React, { useState } from 'react';
import { View } from 'react-native';
import { Divider } from './components/ui/divider';
import { Grid, GridItem } from './components/ui/grid';
import { Fab, FabIcon, FabLabel } from './components/ui/fab';
import { Pressable } from './components/ui/pressable';
import { Text } from './components/ui/text';
import { Button, ButtonText } from './components/ui/button';
import { VStack } from './components/ui/vstack';
import { HStack } from './components/ui/hstack';
import { Box } from './components/ui/box';
import { PlusIcon, CameraIcon, SettingsIcon, ScanIcon } from 'lucide-react-native';

// Test component to validate enhanced Final Components
export function LearniScanFinalComponentsTest() {
  const [selectedCard, setSelectedCard] = useState<string | null>(null);

  return (
    <View style={{ padding: 20, backgroundColor: '#000', position: 'relative' }}>
      <Text color="primary" style={{ marginBottom: 20, fontSize: 18 }}>
        Enhanced Final Components Test
      </Text>

      {/* Divider Variants */}
      <Text color="secondary" style={{ marginBottom: 15, fontSize: 16 }}>
        Divider Color Variants:
      </Text>
      
      <VStack space="md" style={{ marginBottom: 25 }}>
        <VStack space="sm">
          <Text color="tertiary" size="sm">Horizontal Dividers:</Text>
          
          <VStack space="md">
            <VStack space="xs">
              <Text color="muted" size="xs">Default</Text>
              <Divider variant="default" orientation="horizontal" />
            </VStack>
            
            <VStack space="xs">
              <Text color="candyPink" size="xs">Candy Pink</Text>
              <Divider variant="candyPink" orientation="horizontal" />
            </VStack>
            
            <VStack space="xs">
              <Text color="candyPurple" size="xs">Candy Purple</Text>
              <Divider variant="candyPurple" orientation="horizontal" />
            </VStack>
            
            <VStack space="xs">
              <Text color="candyBlue" size="xs">Candy Blue</Text>
              <Divider variant="candyBlue" orientation="horizontal" />
            </VStack>
            
            <VStack space="xs">
              <Text color="muted" size="xs">Glass</Text>
              <Divider variant="glass" orientation="horizontal" />
            </VStack>
          </VStack>
        </VStack>

        <HStack space="lg" style={{ height: 60, alignItems: 'center' }}>
          <Text color="tertiary" size="sm">Vertical:</Text>
          <Divider variant="candyPink" orientation="vertical" />
          <Text color="secondary" size="sm">Section 1</Text>
          <Divider variant="candyPurple" orientation="vertical" />
          <Text color="secondary" size="sm">Section 2</Text>
          <Divider variant="candyBlue" orientation="vertical" />
          <Text color="secondary" size="sm">Section 3</Text>
        </HStack>
      </VStack>

      {/* Grid Glass Variants */}
      <Text color="secondary" style={{ marginBottom: 15, fontSize: 16 }}>
        Grid Glass Variants:
      </Text>
      
      <VStack space="lg" style={{ marginBottom: 25 }}>
        {/* Glass Grid */}
        <VStack space="sm">
          <Text color="tertiary" size="sm">Glass Grid Container:</Text>
          
          <Grid variant="glass" numColumns={2} gap={12}>
            <GridItem colSpan={1}>
              <Box style={{ 
                backgroundColor: 'rgba(255, 107, 157, 0.2)', 
                padding: 16, 
                borderRadius: 8,
                borderWidth: 1,
                borderColor: 'rgba(255, 107, 157, 0.3)'
              }}>
                <Text color="candyPink" size="sm" style={{ fontWeight: '600' }}>
                  Scan Quality
                </Text>
                <Text color="primary" size="xs">Ultra HD</Text>
              </Box>
            </GridItem>
            
            <GridItem colSpan={1}>
              <Box style={{ 
                backgroundColor: 'rgba(168, 85, 247, 0.2)', 
                padding: 16, 
                borderRadius: 8,
                borderWidth: 1,
                borderColor: 'rgba(168, 85, 247, 0.3)'
              }}>
                <Text color="candyPurple" size="sm" style={{ fontWeight: '600' }}>
                  AI Processing
                </Text>
                <Text color="primary" size="xs">Advanced</Text>
              </Box>
            </GridItem>
            
            <GridItem colSpan={2}>
              <Box style={{ 
                backgroundColor: 'rgba(59, 130, 246, 0.2)', 
                padding: 16, 
                borderRadius: 8,
                borderWidth: 1,
                borderColor: 'rgba(59, 130, 246, 0.3)'
              }}>
                <Text color="candyBlue" size="sm" style={{ fontWeight: '600' }}>
                  Cloud Storage
                </Text>
                <Text color="primary" size="xs">Unlimited Premium Storage</Text>
              </Box>
            </GridItem>
          </Grid>
        </VStack>

        {/* Glass Card Grid */}
        <VStack space="sm">
          <Text color="tertiary" size="sm">Glass Card Grid Container:</Text>
          
          <Grid variant="glassCard" numColumns={3} gap={8}>
            <GridItem colSpan={1}>
              <Pressable 
                variant="glass"
                onPress={() => setSelectedCard('recent')}
                style={{ 
                  padding: 12, 
                  borderRadius: 8,
                  backgroundColor: selectedCard === 'recent' ? 'rgba(255, 107, 157, 0.1)' : 'transparent'
                }}
              >
                <VStack space="xs" style={{ alignItems: 'center' }}>
                  <ScanIcon size={20} color="#FF6B9D" />
                  <Text color="primary" size="xs" style={{ textAlign: 'center' }}>
                    Recent
                  </Text>
                </VStack>
              </Pressable>
            </GridItem>
            
            <GridItem colSpan={1}>
              <Pressable 
                variant="glass"
                onPress={() => setSelectedCard('favorites')}
                style={{ 
                  padding: 12, 
                  borderRadius: 8,
                  backgroundColor: selectedCard === 'favorites' ? 'rgba(168, 85, 247, 0.1)' : 'transparent'
                }}
              >
                <VStack space="xs" style={{ alignItems: 'center' }}>
                  <SettingsIcon size={20} color="#A855F7" />
                  <Text color="primary" size="xs" style={{ textAlign: 'center' }}>
                    Settings
                  </Text>
                </VStack>
              </Pressable>
            </GridItem>
            
            <GridItem colSpan={1}>
              <Pressable 
                variant="glass"
                onPress={() => setSelectedCard('cloud')}
                style={{ 
                  padding: 12, 
                  borderRadius: 8,
                  backgroundColor: selectedCard === 'cloud' ? 'rgba(59, 130, 246, 0.1)' : 'transparent'
                }}
              >
                <VStack space="xs" style={{ alignItems: 'center' }}>
                  <CameraIcon size={20} color="#3B82F6" />
                  <Text color="primary" size="xs" style={{ textAlign: 'center' }}>
                    Camera
                  </Text>
                </VStack>
              </Pressable>
            </GridItem>
          </Grid>
        </VStack>
      </VStack>

      {/* FAB Variants */}
      <Text color="secondary" style={{ marginBottom: 15, fontSize: 16 }}>
        FAB (Floating Action Button) Variants:
      </Text>
      
      <VStack space="md" style={{ marginBottom: 25 }}>
        <Text color="tertiary" size="sm">
          FABs are positioned absolutely. Check bottom-right corner for examples.
        </Text>
        
        <HStack space="md" style={{ flexWrap: 'wrap' }}>
          <Button action="candyPink" size="sm">
            <ButtonText>Candy Pink FAB</ButtonText>
          </Button>
          <Button action="candyPurple" size="sm">
            <ButtonText>Candy Purple FAB</ButtonText>
          </Button>
          <Button action="candyBlue" size="sm">
            <ButtonText>Candy Blue FAB</ButtonText>
          </Button>
          <Button action="glass" size="sm">
            <ButtonText>Glass FAB</ButtonText>
          </Button>
        </HStack>
      </VStack>

      {/* Pressable Enhanced States */}
      <Text color="secondary" style={{ marginBottom: 15, fontSize: 16 }}>
        Pressable Enhanced States:
      </Text>
      
      <VStack space="md" style={{ marginBottom: 25 }}>
        <VStack space="sm">
          <Text color="tertiary" size="sm">Interactive Cards:</Text>
          
          <VStack space="md">
            <Pressable 
              variant="enhanced"
              onPress={() => setSelectedCard('scan')}
              style={{ 
                padding: 16, 
                borderRadius: 12,
                backgroundColor: 'rgba(255, 107, 157, 0.1)',
                borderWidth: 1,
                borderColor: 'rgba(255, 107, 157, 0.2)'
              }}
            >
              <HStack space="md" style={{ alignItems: 'center' }}>
                <ScanIcon size={24} color="#FF6B9D" />
                <VStack space="xs" style={{ flex: 1 }}>
                  <Text color="primary" size="md" style={{ fontWeight: '600' }}>
                    Start New Scan
                  </Text>
                  <Text color="secondary" size="sm">
                    Capture and enhance documents with AI
                  </Text>
                </VStack>
              </HStack>
            </Pressable>
            
            <Pressable 
              variant="glass"
              onPress={() => setSelectedCard('settings')}
              style={{ 
                padding: 16, 
                borderRadius: 12,
                backgroundColor: 'rgba(168, 85, 247, 0.1)',
                borderWidth: 1,
                borderColor: 'rgba(168, 85, 247, 0.2)'
              }}
            >
              <HStack space="md" style={{ alignItems: 'center' }}>
                <SettingsIcon size={24} color="#A855F7" />
                <VStack space="xs" style={{ flex: 1 }}>
                  <Text color="primary" size="md" style={{ fontWeight: '600' }}>
                    App Settings
                  </Text>
                  <Text color="secondary" size="sm">
                    Configure preferences and quality
                  </Text>
                </VStack>
              </HStack>
            </Pressable>
          </VStack>
        </VStack>
      </VStack>

      {/* Real-World LearniScan Usage */}
      <Text color="secondary" style={{ marginBottom: 15, fontSize: 16 }}>
        Real-World LearniScan Usage:
      </Text>
      
      <VStack space="lg">
        {/* Dashboard Grid */}
        <VStack space="md">
          <Text color="tertiary" size="sm" style={{ fontWeight: '600' }}>
            Dashboard Overview:
          </Text>
          
          <Grid variant="glass" numColumns={2} gap={16}>
            <GridItem colSpan={2}>
              <VStack space="sm">
                <Text color="primary" size="lg" style={{ fontWeight: 'bold' }}>
                  Today's Activity
                </Text>
                <Divider variant="candyPink" orientation="horizontal" />
              </VStack>
            </GridItem>
            
            <GridItem colSpan={1}>
              <Pressable variant="enhanced" style={{ padding: 16, borderRadius: 12 }}>
                <VStack space="sm" style={{ alignItems: 'center' }}>
                  <Text color="candyPink" size="2xl" style={{ fontWeight: 'bold' }}>
                    12
                  </Text>
                  <Text color="secondary" size="sm">Documents Scanned</Text>
                </VStack>
              </Pressable>
            </GridItem>
            
            <GridItem colSpan={1}>
              <Pressable variant="enhanced" style={{ padding: 16, borderRadius: 12 }}>
                <VStack space="sm" style={{ alignItems: 'center' }}>
                  <Text color="candyPurple" size="2xl" style={{ fontWeight: 'bold' }}>
                    98%
                  </Text>
                  <Text color="secondary" size="sm">AI Accuracy</Text>
                </VStack>
              </Pressable>
            </GridItem>
            
            <GridItem colSpan={2}>
              <VStack space="sm">
                <Divider variant="glass" orientation="horizontal" />
                <HStack style={{ justifyContent: 'space-between' }}>
                  <Text color="tertiary" size="sm">Storage Used</Text>
                  <Text color="candyBlue" size="sm">2.4 GB / 10 GB</Text>
                </HStack>
              </VStack>
            </GridItem>
          </Grid>
        </VStack>
      </VStack>

      {/* Floating Action Buttons */}
      <Fab variant="candyPink" size="lg" placement="bottom right">
        <FabIcon as={PlusIcon} />
      </Fab>
      
      <Fab variant="glass" size="md" placement="bottom left">
        <FabIcon as={CameraIcon} />
      </Fab>
    </View>
  );
}