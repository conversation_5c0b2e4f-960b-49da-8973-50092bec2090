<!DOCTYPE html><html class="default" lang="en" data-base="."><head><meta charset="utf-8"/><meta http-equiv="x-ua-compatible" content="IE=edge"/><title>Expo Template Documentation</title><meta name="description" content="Documentation for Expo Template Documentation"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="assets/style.css"/><link rel="stylesheet" href="assets/highlight.css"/><script defer src="assets/main.js"></script><script async src="assets/icons.js" id="tsd-icons-script"></script><script async src="assets/search.js" id="tsd-search-script"></script><script async src="assets/navigation.js" id="tsd-nav-script"></script></head><body><script>document.documentElement.dataset.theme = localStorage.getItem("tsd-theme") || "os";document.body.style.display="none";setTimeout(() => app?app.showPage():document.body.style.removeProperty("display"),500)</script><header class="tsd-page-toolbar"><div class="tsd-toolbar-contents container"><div class="table-cell" id="tsd-search"><div class="field"><label for="tsd-search-field" class="tsd-widget tsd-toolbar-icon search no-caption"><svg width="16" height="16" viewBox="0 0 16 16" fill="none"><use href="assets/icons.svg#icon-search"></use></svg></label><input type="text" id="tsd-search-field" aria-label="Search"/></div><div class="field"><div id="tsd-toolbar-links"></div></div><ul class="results"><li class="state loading">Preparing search index...</li><li class="state failure">The search index is not available</li></ul><a href="index.html" class="title">Expo Template Documentation</a></div><div class="table-cell" id="tsd-widgets"><a href="#" class="tsd-widget tsd-toolbar-icon menu no-caption" data-toggle="menu" aria-label="Menu"><svg width="16" height="16" viewBox="0 0 16 16" fill="none"><use href="assets/icons.svg#icon-menu"></use></svg></a></div></div></header><div class="container container-main"><div class="col-content"><div class="tsd-page-title"><h1>Expo Template Documentation</h1></div><div class="tsd-panel tsd-typography"><a id="一个-expo-开发模板" class="tsd-anchor"></a><h1 class="tsd-anchor-link">一个 Expo 开发模板<a href="#一个-expo-开发模板" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="assets/icons.svg#icon-anchor"></use></svg></a></h1><a id="特性" class="tsd-anchor"></a><h2 class="tsd-anchor-link">特性<a href="#特性" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="assets/icons.svg#icon-anchor"></use></svg></a></h2><ul>
<li>nativewind，支持 tailwindcss方式写精美的统一样式</li>
<li>expo-auth-session，支持 oauth 登陆</li>
<li>expo-localization+i18n,国际化开箱即用</li>
<li>轻松暗黑/亮白模式切换，支持动态切换</li>
<li>基于文件的路由，省心，不需要配置</li>
</ul>
<a id="安装依赖" class="tsd-anchor"></a><h2 class="tsd-anchor-link">安装依赖<a href="#安装依赖" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="assets/icons.svg#icon-anchor"></use></svg></a></h2><pre><code class="shell"><span class="hl-0">npm</span><span class="hl-1"> </span><span class="hl-2">install</span>
</code><button type="button">Copy</button></pre>

<a id="运行" class="tsd-anchor"></a><h2 class="tsd-anchor-link">运行<a href="#运行" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="assets/icons.svg#icon-anchor"></use></svg></a></h2><pre><code class="shell"><span class="hl-0">npm</span><span class="hl-1"> </span><span class="hl-2">run</span><span class="hl-1"> </span><span class="hl-2">start</span>
</code><button type="button">Copy</button></pre>

<a id="生成打包文件" class="tsd-anchor"></a><h2 class="tsd-anchor-link">生成打包文件<a href="#生成打包文件" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="assets/icons.svg#icon-anchor"></use></svg></a></h2><pre><code class="shell"><span class="hl-0">npm</span><span class="hl-1"> </span><span class="hl-2">run</span><span class="hl-1"> </span><span class="hl-2">build:android</span>
</code><button type="button">Copy</button></pre>

<a id="打开原生开发模式" class="tsd-anchor"></a><h3 class="tsd-anchor-link">打开原生开发模式<a href="#打开原生开发模式" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="assets/icons.svg#icon-anchor"></use></svg></a></h3><pre><code class="shell"><span class="hl-0">npx</span><span class="hl-1"> </span><span class="hl-2">expo</span><span class="hl-1"> </span><span class="hl-2">prebuild</span>
</code><button type="button">Copy</button></pre>

<p>这个操作会生成一个 <code>ios</code> 和 <code>android</code> 的工程文件，然后执行</p>
<pre><code class="shell"><span class="hl-0">npx</span><span class="hl-1"> </span><span class="hl-2">pod-install</span>
</code><button type="button">Copy</button></pre>

<a id="oauth登陆配置" class="tsd-anchor"></a><h3 class="tsd-anchor-link">oauth登陆配置<a href="#oauth登陆配置" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="assets/icons.svg#icon-anchor"></use></svg></a></h3><ul>
<li><a href="https://docs.expo.dev/guides/authentication/" target="_blank" class="external">配置 oauth</a></li>
<li>.env 中配置响应的 key,</li>
<li>然后在 <code>app.config.js</code> 去增加相应的配置，已经有 github 的配置示例，可以参考。</li>
</ul>
</div></div><div class="col-sidebar"><div class="page-menu"><div class="tsd-navigation settings"><details class="tsd-accordion"><summary class="tsd-accordion-summary"><h3><svg width="20" height="20" viewBox="0 0 24 24" fill="none"><use href="assets/icons.svg#icon-chevronDown"></use></svg>Settings</h3></summary><div class="tsd-accordion-details"><div class="tsd-filter-visibility"><span class="settings-label">Member Visibility</span><ul id="tsd-filter-options"><li class="tsd-filter-item"><label class="tsd-filter-input"><input type="checkbox" id="tsd-filter-inherited" name="inherited" checked/><svg width="32" height="32" viewBox="0 0 32 32" aria-hidden="true"><rect class="tsd-checkbox-background" width="30" height="30" x="1" y="1" rx="6" fill="none"></rect><path class="tsd-checkbox-checkmark" d="M8.35422 16.8214L13.2143 21.75L24.6458 10.25" stroke="none" stroke-width="3.5" stroke-linejoin="round" fill="none"></path></svg><span>Inherited</span></label></li></ul></div><div class="tsd-theme-toggle"><label class="settings-label" for="tsd-theme">Theme</label><select id="tsd-theme"><option value="os">OS</option><option value="light">Light</option><option value="dark">Dark</option></select></div></div></details></div><details open class="tsd-accordion tsd-page-navigation"><summary class="tsd-accordion-summary"><h3><svg width="20" height="20" viewBox="0 0 24 24" fill="none"><use href="assets/icons.svg#icon-chevronDown"></use></svg>On This Page</h3></summary><div class="tsd-accordion-details"><a href="#一个-expo-开发模板"><span>一个 <wbr/>Expo 开发模板</span></a><ul><li><a href="#特性"><span>特性</span></a></li><li><a href="#安装依赖"><span>安装依赖</span></a></li><li><a href="#运行"><span>运行</span></a></li><li><a href="#生成打包文件"><span>生成打包文件</span></a></li><li><ul><li><a href="#打开原生开发模式"><span>打开原生开发模式</span></a></li><li><a href="#oauth登陆配置"><span>oauth登陆配置</span></a></li></ul></li></ul></div></details></div><div class="site-menu"><nav class="tsd-navigation"><a href="modules.html" class="current">Expo Template Documentation</a><ul class="tsd-small-nested-navigation" id="tsd-nav-container"><li>Loading...</li></ul></nav></div></div></div><footer><p class="tsd-generator">Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p></footer><div class="overlay"></div></body></html>
