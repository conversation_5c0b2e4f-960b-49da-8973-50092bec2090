# LearniScan AppWrite Implementation Guide

## Overview

This guide provides step-by-step instructions for integrating AppWrite into the LearniScan React Native application. The implementation follows a phased approach to ensure smooth integration with existing features.

## Prerequisites

- Node.js 18+ and npm/yarn
- React Native development environment
- Expo CLI
- AppWrite Cloud account or self-hosted instance
- Basic understanding of TypeScript and React Native

## Phase 1: AppWrite Project Setup & Configuration

### Step 1: Create AppWrite Project

1. **Sign up for AppWrite Cloud**
   ```bash
   # Visit https://cloud.appwrite.io
   # Create account and new project
   # Note down Project ID and API Endpoint
   ```

2. **Configure Project Settings**
   - Project Name: `LearniScan`
   - Project ID: `learni-scan-[environment]`
   - Region: Choose closest to your users

### Step 2: Configure Authentication

1. **Enable Authentication Providers**
   ```javascript
   // In AppWrite Console > Auth > Settings
   
   // Email/Password
   - Enable Email/Password authentication
   - Set session length: 365 days
   - Enable email verification
   
   // OAuth Providers
   - Google OAuth: Configure with client ID/secret
   - GitHub OAuth: Configure with client ID/secret
   - Apple Sign-In: Configure for iOS
   ```

2. **Set up Email Templates**
   ```html
   <!-- Email Verification Template -->
   <h2>Welcome to LearniScan!</h2>
   <p>Please verify your email address by clicking the link below:</p>
   <a href="{{url}}">Verify Email</a>
   
   <!-- Password Reset Template -->
   <h2>Reset Your Password</h2>
   <p>Click the link below to reset your password:</p>
   <a href="{{url}}">Reset Password</a>
   ```

### Step 3: Create Database Schema

1. **Create Database**
   ```javascript
   // Database ID: 'learni-scan-db'
   // Name: 'LearniScan Database'
   ```

2. **Create Collections**

   **Users Collection (`users`)**
   ```javascript
   // Collection ID: 'users'
   // Attributes:
   {
     email: { type: 'string', size: 255, required: true },
     name: { type: 'string', size: 100, required: true },
     avatar: { type: 'string', size: 500, required: false },
     preferences: { type: 'string', size: 2000, required: false }, // JSON
     learningStats: { type: 'string', size: 1000, required: false }, // JSON
     subscription: { type: 'string', size: 500, required: false }, // JSON
     createdAt: { type: 'datetime', required: true },
     updatedAt: { type: 'datetime', required: true }
   }
   
   // Indexes:
   - email (unique)
   - createdAt
   ```

   **Knowledge Cards Collection (`knowledge_cards`)**
   ```javascript
   // Collection ID: 'knowledge_cards'
   // Attributes:
   {
     userId: { type: 'string', size: 50, required: true },
     title: { type: 'string', size: 200, required: true },
     content: { type: 'string', size: 10000, required: true },
     sourceType: { type: 'string', size: 50, required: true },
     sourceData: { type: 'string', size: 5000, required: false }, // JSON
     tags: { type: 'string', size: 1000, required: false }, // JSON array
     difficulty: { type: 'string', size: 20, required: true },
     category: { type: 'string', size: 100, required: true },
     reviewData: { type: 'string', size: 1000, required: true }, // JSON
     aiEnhanced: { type: 'boolean', required: true },
     isPublic: { type: 'boolean', required: true },
     createdAt: { type: 'datetime', required: true },
     updatedAt: { type: 'datetime', required: true }
   }
   
   // Indexes:
   - userId
   - category
   - difficulty
   - createdAt
   - isPublic
   ```

   **Learning Sessions Collection (`learning_sessions`)**
   ```javascript
   // Collection ID: 'learning_sessions'
   // Attributes:
   {
     userId: { type: 'string', size: 50, required: true },
     sessionType: { type: 'string', size: 20, required: true },
     cardsReviewed: { type: 'string', size: 2000, required: true }, // JSON array
     performance: { type: 'string', size: 1000, required: true }, // JSON
     duration: { type: 'integer', required: true },
     startedAt: { type: 'datetime', required: true },
     completedAt: { type: 'datetime', required: true }
   }
   
   // Indexes:
   - userId
   - sessionType
   - startedAt
   ```

   **Scan History Collection (`scan_history`)**
   ```javascript
   // Collection ID: 'scan_history'
   // Attributes:
   {
     userId: { type: 'string', size: 50, required: true },
     originalImageId: { type: 'string', size: 50, required: true },
     extractedText: { type: 'string', size: 20000, required: true },
     processedContent: { type: 'string', size: 20000, required: false },
     knowledgeCardId: { type: 'string', size: 50, required: false },
     scanType: { type: 'string', size: 20, required: true },
     confidence: { type: 'float', required: true },
     language: { type: 'string', size: 10, required: true },
     metadata: { type: 'string', size: 1000, required: true }, // JSON
     createdAt: { type: 'datetime', required: true }
   }
   
   // Indexes:
   - userId
   - scanType
   - createdAt
   ```

### Step 4: Configure Storage Buckets

1. **User Avatars Bucket**
   ```javascript
   // Bucket ID: 'user-avatars'
   // Name: 'User Profile Pictures'
   // Permissions: User read/write own files
   // File Size Limit: 5MB
   // Allowed File Extensions: jpg, jpeg, png, webp
   // Compression: Enabled
   // Encryption: Enabled
   ```

2. **Scan Images Bucket**
   ```javascript
   // Bucket ID: 'scan-images'
   // Name: 'Scanned Documents and Images'
   // Permissions: User read/write own files
   // File Size Limit: 20MB
   // Allowed File Extensions: jpg, jpeg, png, pdf
   // Compression: Disabled (preserve quality)
   // Encryption: Enabled
   ```

3. **Knowledge Card Assets Bucket**
   ```javascript
   // Bucket ID: 'card-assets'
   // Name: 'Knowledge Card Images and Assets'
   // Permissions: User read/write own files, public read for shared
   // File Size Limit: 10MB
   // Allowed File Extensions: jpg, jpeg, png, svg, gif
   // Compression: Enabled
   // Encryption: Enabled
   // CDN: Enabled
   ```

4. **User Exports Bucket**
   ```javascript
   // Bucket ID: 'user-exports'
   // Name: 'User Data Exports'
   // Permissions: User read/write own files
   // File Size Limit: 50MB
   // Allowed File Extensions: pdf, json, csv, zip
   // Compression: Enabled
   // Encryption: Enabled
   // Auto-delete: 30 days
   ```

## Phase 2: React Native SDK Installation & Configuration

### Step 1: Install Dependencies

```bash
# Install AppWrite SDK and dependencies
npx expo install react-native-appwrite react-native-url-polyfill

# Install additional utilities
npm install @react-native-async-storage/async-storage
```

### Step 2: Configure Environment Variables

1. **Create Environment Configuration**
   ```typescript
   // lib/config/appwrite.ts
   import { Client, Account, Databases, Storage, Functions } from 'react-native-appwrite';
   
   // Environment configuration
   export const APPWRITE_CONFIG = {
     endpoint: process.env.EXPO_PUBLIC_APPWRITE_ENDPOINT || 'https://cloud.appwrite.io/v1',
     projectId: process.env.EXPO_PUBLIC_APPWRITE_PROJECT_ID || '',
     databaseId: process.env.EXPO_PUBLIC_APPWRITE_DATABASE_ID || 'learni-scan-db',
     
     // Collection IDs
     collections: {
       users: 'users',
       knowledgeCards: 'knowledge_cards',
       learningSessions: 'learning_sessions',
       scanHistory: 'scan_history',
     },
     
     // Storage Bucket IDs
     buckets: {
       userAvatars: 'user-avatars',
       scanImages: 'scan-images',
       cardAssets: 'card-assets',
       userExports: 'user-exports',
     },
   };
   
   // Initialize AppWrite client
   export const client = new Client()
     .setEndpoint(APPWRITE_CONFIG.endpoint)
     .setProject(APPWRITE_CONFIG.projectId)
     .setPlatform('com.learni.scan'); // Your app bundle ID
   
   // Initialize services
   export const account = new Account(client);
   export const databases = new Databases(client);
   export const storage = new Storage(client);
   export const functions = new Functions(client);
   ```

2. **Update app.config.js**
   ```javascript
   // app.config.js
   export default {
     expo: {
       // ... existing config
       extra: {
         // ... existing extra config
         appwriteEndpoint: process.env.EXPO_PUBLIC_APPWRITE_ENDPOINT,
         appwriteProjectId: process.env.EXPO_PUBLIC_APPWRITE_PROJECT_ID,
         appwriteDatabaseId: process.env.EXPO_PUBLIC_APPWRITE_DATABASE_ID,
       },
     },
   };
   ```

3. **Create .env files**
   ```bash
   # .env.local
   EXPO_PUBLIC_APPWRITE_ENDPOINT=https://cloud.appwrite.io/v1
   EXPO_PUBLIC_APPWRITE_PROJECT_ID=your-project-id
   EXPO_PUBLIC_APPWRITE_DATABASE_ID=learni-scan-db
   
   # .env.production
   EXPO_PUBLIC_APPWRITE_ENDPOINT=https://your-production-endpoint.com/v1
   EXPO_PUBLIC_APPWRITE_PROJECT_ID=your-production-project-id
   EXPO_PUBLIC_APPWRITE_DATABASE_ID=learni-scan-db
   ```

### Step 3: Create AppWrite Service Layer

1. **Authentication Service**
   ```typescript
   // lib/services/auth.service.ts
   import { ID, OAuthProvider } from 'react-native-appwrite';
   import { account } from '@/lib/config/appwrite';
   import type { User } from '@/types/user';
   
   export class AuthService {
     // Email/Password Authentication
     async signUp(email: string, password: string, name: string): Promise<User> {
       try {
         const user = await account.create(ID.unique(), email, password, name);
         await account.createEmailPasswordSession(email, password);
         return this.getCurrentUser();
       } catch (error) {
         throw new Error(`Sign up failed: ${error.message}`);
       }
     }
   
     async signIn(email: string, password: string): Promise<User> {
       try {
         await account.createEmailPasswordSession(email, password);
         return this.getCurrentUser();
       } catch (error) {
         throw new Error(`Sign in failed: ${error.message}`);
       }
     }
   
     // OAuth Authentication
     async signInWithOAuth(provider: OAuthProvider): Promise<void> {
       try {
         await account.createOAuth2Session(
           provider,
           'learni-scan://auth/success',
           'learni-scan://auth/failure'
         );
       } catch (error) {
         throw new Error(`OAuth sign in failed: ${error.message}`);
       }
     }
   
     // Session Management
     async getCurrentUser(): Promise<User | null> {
       try {
         const user = await account.get();
         return {
           id: user.$id,
           email: user.email,
           name: user.name,
           emailVerification: user.emailVerification,
           createdAt: user.$createdAt,
         };
       } catch (error) {
         return null;
       }
     }
   
     async signOut(): Promise<void> {
       try {
         await account.deleteSession('current');
       } catch (error) {
         throw new Error(`Sign out failed: ${error.message}`);
       }
     }
   
     // Account Management
     async updateProfile(data: { name?: string; email?: string }): Promise<User> {
       try {
         if (data.name) {
           await account.updateName(data.name);
         }
         if (data.email) {
           await account.updateEmail(data.email, 'current-password');
         }
         return this.getCurrentUser();
       } catch (error) {
         throw new Error(`Profile update failed: ${error.message}`);
       }
     }
   
     async changePassword(newPassword: string, oldPassword: string): Promise<void> {
       try {
         await account.updatePassword(newPassword, oldPassword);
       } catch (error) {
         throw new Error(`Password change failed: ${error.message}`);
       }
     }
   
     async resetPassword(email: string): Promise<void> {
       try {
         await account.createRecovery(
           email,
           'learni-scan://auth/reset-password'
         );
       } catch (error) {
         throw new Error(`Password reset failed: ${error.message}`);
       }
     }
   
     async verifyEmail(userId: string, secret: string): Promise<void> {
       try {
         await account.updateVerification(userId, secret);
       } catch (error) {
         throw new Error(`Email verification failed: ${error.message}`);
       }
     }
   }
   
   export const authService = new AuthService();
   ```

## Phase 3: Database Integration

### Step 1: Create Database Service

```typescript
// lib/services/database.service.ts
import { ID, Query } from 'react-native-appwrite';
import { databases } from '@/lib/config/appwrite';
import { APPWRITE_CONFIG } from '@/lib/config/appwrite';
import type { KnowledgeCard, LearningSession, ScanHistory } from '@/types';

export class DatabaseService {
  private databaseId = APPWRITE_CONFIG.databaseId;
  private collections = APPWRITE_CONFIG.collections;

  // Knowledge Cards
  async createKnowledgeCard(data: Omit<KnowledgeCard, '$id' | 'createdAt' | 'updatedAt'>): Promise<KnowledgeCard> {
    try {
      const now = new Date().toISOString();
      const result = await databases.createDocument(
        this.databaseId,
        this.collections.knowledgeCards,
        ID.unique(),
        {
          ...data,
          createdAt: now,
          updatedAt: now,
        }
      );
      return result as KnowledgeCard;
    } catch (error) {
      throw new Error(`Failed to create knowledge card: ${error.message}`);
    }
  }

  async getKnowledgeCards(userId: string, limit = 25, offset = 0): Promise<KnowledgeCard[]> {
    try {
      const result = await databases.listDocuments(
        this.databaseId,
        this.collections.knowledgeCards,
        [
          Query.equal('userId', userId),
          Query.orderDesc('createdAt'),
          Query.limit(limit),
          Query.offset(offset),
        ]
      );
      return result.documents as KnowledgeCard[];
    } catch (error) {
      throw new Error(`Failed to get knowledge cards: ${error.message}`);
    }
  }

  async updateKnowledgeCard(cardId: string, data: Partial<KnowledgeCard>): Promise<KnowledgeCard> {
    try {
      const result = await databases.updateDocument(
        this.databaseId,
        this.collections.knowledgeCards,
        cardId,
        {
          ...data,
          updatedAt: new Date().toISOString(),
        }
      );
      return result as KnowledgeCard;
    } catch (error) {
      throw new Error(`Failed to update knowledge card: ${error.message}`);
    }
  }

  async deleteKnowledgeCard(cardId: string): Promise<void> {
    try {
      await databases.deleteDocument(
        this.databaseId,
        this.collections.knowledgeCards,
        cardId
      );
    } catch (error) {
      throw new Error(`Failed to delete knowledge card: ${error.message}`);
    }
  }

  // Learning Sessions
  async createLearningSession(data: Omit<LearningSession, '$id'>): Promise<LearningSession> {
    try {
      const result = await databases.createDocument(
        this.databaseId,
        this.collections.learningSessions,
        ID.unique(),
        data
      );
      return result as LearningSession;
    } catch (error) {
      throw new Error(`Failed to create learning session: ${error.message}`);
    }
  }

  async getLearningHistory(userId: string, limit = 50): Promise<LearningSession[]> {
    try {
      const result = await databases.listDocuments(
        this.databaseId,
        this.collections.learningSessions,
        [
          Query.equal('userId', userId),
          Query.orderDesc('startedAt'),
          Query.limit(limit),
        ]
      );
      return result.documents as LearningSession[];
    } catch (error) {
      throw new Error(`Failed to get learning history: ${error.message}`);
    }
  }

  // Scan History
  async createScanRecord(data: Omit<ScanHistory, '$id'>): Promise<ScanHistory> {
    try {
      const result = await databases.createDocument(
        this.databaseId,
        this.collections.scanHistory,
        ID.unique(),
        data
      );
      return result as ScanHistory;
    } catch (error) {
      throw new Error(`Failed to create scan record: ${error.message}`);
    }
  }

  async getScanHistory(userId: string, limit = 50): Promise<ScanHistory[]> {
    try {
      const result = await databases.listDocuments(
        this.databaseId,
        this.collections.scanHistory,
        [
          Query.equal('userId', userId),
          Query.orderDesc('createdAt'),
          Query.limit(limit),
        ]
      );
      return result.documents as ScanHistory[];
    } catch (error) {
      throw new Error(`Failed to get scan history: ${error.message}`);
    }
  }
}

export const databaseService = new DatabaseService();
```

## Phase 4: File Storage Integration

### Step 1: Create Storage Service

```typescript
// lib/services/storage.service.ts
import { ID, Permission, Role } from 'react-native-appwrite';
import { storage } from '@/lib/config/appwrite';
import { APPWRITE_CONFIG } from '@/lib/config/appwrite';

export interface FileUploadProgress {
  progress: number;
  total: number;
  loaded: number;
}

export interface FileUploadResult {
  $id: string;
  name: string;
  signature: string;
  mimeType: string;
  sizeOriginal: number;
  $createdAt: string;
  $updatedAt: string;
}

export class StorageService {
  private buckets = APPWRITE_CONFIG.buckets;

  // Upload file with progress tracking
  async uploadFile(
    bucketId: string,
    file: any, // File object from react-native-image-picker or similar
    permissions?: string[],
    onProgress?: (progress: FileUploadProgress) => void
  ): Promise<FileUploadResult> {
    try {
      const fileId = ID.unique();

      // Default permissions: user can read/write their own files
      const defaultPermissions = permissions || [
        Permission.read(Role.user(file.userId)),
        Permission.write(Role.user(file.userId)),
        Permission.update(Role.user(file.userId)),
        Permission.delete(Role.user(file.userId)),
      ];

      const result = await storage.createFile(
        bucketId,
        fileId,
        file,
        defaultPermissions,
        onProgress
      );

      return result as FileUploadResult;
    } catch (error) {
      throw new Error(`File upload failed: ${error.message}`);
    }
  }

  // Upload user avatar
  async uploadAvatar(userId: string, file: any): Promise<FileUploadResult> {
    return this.uploadFile(
      this.buckets.userAvatars,
      file,
      [
        Permission.read(Role.user(userId)),
        Permission.write(Role.user(userId)),
        Permission.update(Role.user(userId)),
        Permission.delete(Role.user(userId)),
      ]
    );
  }

  // Upload scan image
  async uploadScanImage(userId: string, file: any, onProgress?: (progress: FileUploadProgress) => void): Promise<FileUploadResult> {
    return this.uploadFile(
      this.buckets.scanImages,
      file,
      [
        Permission.read(Role.user(userId)),
        Permission.write(Role.user(userId)),
        Permission.delete(Role.user(userId)),
      ],
      onProgress
    );
  }

  // Upload knowledge card asset
  async uploadCardAsset(userId: string, file: any, isPublic = false): Promise<FileUploadResult> {
    const permissions = isPublic
      ? [
          Permission.read(Role.any()), // Public read
          Permission.write(Role.user(userId)),
          Permission.update(Role.user(userId)),
          Permission.delete(Role.user(userId)),
        ]
      : [
          Permission.read(Role.user(userId)),
          Permission.write(Role.user(userId)),
          Permission.update(Role.user(userId)),
          Permission.delete(Role.user(userId)),
        ];

    return this.uploadFile(this.buckets.cardAssets, file, permissions);
  }

  // Get file download URL
  getFileUrl(bucketId: string, fileId: string): string {
    return storage.getFileDownload(bucketId, fileId);
  }

  // Get file preview URL (for images)
  getFilePreview(
    bucketId: string,
    fileId: string,
    width?: number,
    height?: number,
    gravity?: string,
    quality?: number
  ): string {
    return storage.getFilePreview(
      bucketId,
      fileId,
      width,
      height,
      gravity,
      quality
    );
  }

  // Get file view URL
  getFileView(bucketId: string, fileId: string): string {
    return storage.getFileView(bucketId, fileId);
  }

  // Delete file
  async deleteFile(bucketId: string, fileId: string): Promise<void> {
    try {
      await storage.deleteFile(bucketId, fileId);
    } catch (error) {
      throw new Error(`File deletion failed: ${error.message}`);
    }
  }

  // Get file metadata
  async getFile(bucketId: string, fileId: string): Promise<any> {
    try {
      return await storage.getFile(bucketId, fileId);
    } catch (error) {
      throw new Error(`Failed to get file metadata: ${error.message}`);
    }
  }

  // List files in bucket
  async listFiles(bucketId: string, queries?: string[]): Promise<any> {
    try {
      return await storage.listFiles(bucketId, queries);
    } catch (error) {
      throw new Error(`Failed to list files: ${error.message}`);
    }
  }

  // Batch upload multiple files
  async uploadMultipleFiles(
    bucketId: string,
    files: any[],
    permissions?: string[],
    onProgress?: (fileIndex: number, progress: FileUploadProgress) => void
  ): Promise<FileUploadResult[]> {
    const results: FileUploadResult[] = [];

    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const result = await this.uploadFile(
        bucketId,
        file,
        permissions,
        onProgress ? (progress) => onProgress(i, progress) : undefined
      );
      results.push(result);
    }

    return results;
  }
}

export const storageService = new StorageService();
```

## Phase 5: Real-time Subscriptions

### Step 1: Create Real-time Service

```typescript
// lib/services/realtime.service.ts
import { client } from '@/lib/config/appwrite';
import { APPWRITE_CONFIG } from '@/lib/config/appwrite';

export type RealtimeCallback = (payload: any) => void;

export class RealtimeService {
  private subscriptions: Map<string, () => void> = new Map();

  // Subscribe to user-specific updates
  subscribeToUserUpdates(userId: string, callback: RealtimeCallback): () => void {
    const channel = `databases.${APPWRITE_CONFIG.databaseId}.collections.${APPWRITE_CONFIG.collections.users}.documents.${userId}`;

    const unsubscribe = client.subscribe(channel, callback);

    const subscriptionId = `user-${userId}`;
    this.subscriptions.set(subscriptionId, unsubscribe);

    return () => {
      unsubscribe();
      this.subscriptions.delete(subscriptionId);
    };
  }

  // Subscribe to knowledge card updates for a user
  subscribeToKnowledgeCards(userId: string, callback: RealtimeCallback): () => void {
    const channel = `databases.${APPWRITE_CONFIG.databaseId}.collections.${APPWRITE_CONFIG.collections.knowledgeCards}.documents`;

    const unsubscribe = client.subscribe(channel, (response) => {
      // Filter for user's cards only
      if (response.payload.userId === userId) {
        callback(response);
      }
    });

    const subscriptionId = `cards-${userId}`;
    this.subscriptions.set(subscriptionId, unsubscribe);

    return () => {
      unsubscribe();
      this.subscriptions.delete(subscriptionId);
    };
  }

  // Subscribe to learning session updates
  subscribeToLearningSessions(userId: string, callback: RealtimeCallback): () => void {
    const channel = `databases.${APPWRITE_CONFIG.databaseId}.collections.${APPWRITE_CONFIG.collections.learningSessions}.documents`;

    const unsubscribe = client.subscribe(channel, (response) => {
      if (response.payload.userId === userId) {
        callback(response);
      }
    });

    const subscriptionId = `sessions-${userId}`;
    this.subscriptions.set(subscriptionId, unsubscribe);

    return () => {
      unsubscribe();
      this.subscriptions.delete(subscriptionId);
    };
  }

  // Subscribe to specific knowledge card
  subscribeToCard(cardId: string, callback: RealtimeCallback): () => void {
    const channel = `databases.${APPWRITE_CONFIG.databaseId}.collections.${APPWRITE_CONFIG.collections.knowledgeCards}.documents.${cardId}`;

    const unsubscribe = client.subscribe(channel, callback);

    const subscriptionId = `card-${cardId}`;
    this.subscriptions.set(subscriptionId, unsubscribe);

    return () => {
      unsubscribe();
      this.subscriptions.delete(subscriptionId);
    };
  }

  // Unsubscribe from all subscriptions
  unsubscribeAll(): void {
    this.subscriptions.forEach((unsubscribe) => {
      unsubscribe();
    });
    this.subscriptions.clear();
  }

  // Get active subscription count
  getActiveSubscriptionCount(): number {
    return this.subscriptions.size;
  }
}

export const realtimeService = new RealtimeService();
```

## Phase 6: Error Handling & Offline Capabilities

### Step 1: Create Error Handling Service

```typescript
// lib/services/error.service.ts
import AsyncStorage from '@react-native-async-storage/async-storage';

export interface AppwriteError {
  code: number;
  message: string;
  type: string;
}

export interface OfflineOperation {
  id: string;
  type: 'create' | 'update' | 'delete';
  collection: string;
  data: any;
  timestamp: number;
  retryCount: number;
}

export class ErrorHandlingService {
  private readonly OFFLINE_QUEUE_KEY = 'appwrite_offline_queue';
  private readonly MAX_RETRY_COUNT = 3;
  private readonly RETRY_DELAY = 1000; // 1 second

  // Handle AppWrite errors
  handleError(error: any): string {
    if (error.code) {
      switch (error.code) {
        case 401:
          return 'Authentication required. Please sign in again.';
        case 403:
          return 'Access denied. You don\'t have permission for this action.';
        case 404:
          return 'Resource not found.';
        case 409:
          return 'Conflict. This resource already exists.';
        case 429:
          return 'Too many requests. Please try again later.';
        case 500:
          return 'Server error. Please try again later.';
        case 503:
          return 'Service unavailable. Please check your connection.';
        default:
          return error.message || 'An unexpected error occurred.';
      }
    }

    return error.message || 'An unexpected error occurred.';
  }

  // Queue operation for offline execution
  async queueOfflineOperation(operation: Omit<OfflineOperation, 'id' | 'timestamp' | 'retryCount'>): Promise<void> {
    try {
      const queue = await this.getOfflineQueue();
      const newOperation: OfflineOperation = {
        ...operation,
        id: Date.now().toString(),
        timestamp: Date.now(),
        retryCount: 0,
      };

      queue.push(newOperation);
      await AsyncStorage.setItem(this.OFFLINE_QUEUE_KEY, JSON.stringify(queue));
    } catch (error) {
      console.error('Failed to queue offline operation:', error);
    }
  }

  // Get offline operation queue
  async getOfflineQueue(): Promise<OfflineOperation[]> {
    try {
      const queueData = await AsyncStorage.getItem(this.OFFLINE_QUEUE_KEY);
      return queueData ? JSON.parse(queueData) : [];
    } catch (error) {
      console.error('Failed to get offline queue:', error);
      return [];
    }
  }

  // Process offline queue when connection is restored
  async processOfflineQueue(): Promise<void> {
    try {
      const queue = await this.getOfflineQueue();
      const processedOperations: string[] = [];

      for (const operation of queue) {
        try {
          await this.executeOfflineOperation(operation);
          processedOperations.push(operation.id);
        } catch (error) {
          // Increment retry count
          operation.retryCount++;

          if (operation.retryCount >= this.MAX_RETRY_COUNT) {
            console.error(`Max retry count reached for operation ${operation.id}:`, error);
            processedOperations.push(operation.id); // Remove from queue
          } else {
            console.warn(`Retry ${operation.retryCount} failed for operation ${operation.id}:`, error);
          }
        }
      }

      // Remove processed operations from queue
      const remainingQueue = queue.filter(op => !processedOperations.includes(op.id));
      await AsyncStorage.setItem(this.OFFLINE_QUEUE_KEY, JSON.stringify(remainingQueue));

    } catch (error) {
      console.error('Failed to process offline queue:', error);
    }
  }

  // Execute a single offline operation
  private async executeOfflineOperation(operation: OfflineOperation): Promise<void> {
    // Import services dynamically to avoid circular dependencies
    const { databaseService } = await import('./database.service');

    switch (operation.type) {
      case 'create':
        if (operation.collection === 'knowledge_cards') {
          await databaseService.createKnowledgeCard(operation.data);
        }
        break;

      case 'update':
        if (operation.collection === 'knowledge_cards') {
          await databaseService.updateKnowledgeCard(operation.data.id, operation.data);
        }
        break;

      case 'delete':
        if (operation.collection === 'knowledge_cards') {
          await databaseService.deleteKnowledgeCard(operation.data.id);
        }
        break;

      default:
        throw new Error(`Unknown operation type: ${operation.type}`);
    }
  }

  // Clear offline queue
  async clearOfflineQueue(): Promise<void> {
    try {
      await AsyncStorage.removeItem(this.OFFLINE_QUEUE_KEY);
    } catch (error) {
      console.error('Failed to clear offline queue:', error);
    }
  }

  // Retry operation with exponential backoff
  async retryOperation<T>(
    operation: () => Promise<T>,
    maxRetries = 3,
    baseDelay = 1000
  ): Promise<T> {
    let lastError: any;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;

        if (attempt === maxRetries) {
          break;
        }

        const delay = baseDelay * Math.pow(2, attempt);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    throw lastError;
  }
}

export const errorHandlingService = new ErrorHandlingService();
```

## Next Steps

This implementation guide now covers:

1. ✅ **AppWrite Project Setup & Configuration**
2. ✅ **React Native SDK Installation & Configuration**
3. ✅ **Database Integration**
4. ✅ **File Storage Integration**
5. ✅ **Real-time Subscriptions**
6. ✅ **Error Handling & Offline Capabilities**

The final phases will include:
- **State Management Integration** - Connecting AppWrite services with Zustand stores
- **UI Component Integration** - Updating existing components to use AppWrite
- **Testing & Documentation** - Comprehensive testing and documentation updates
