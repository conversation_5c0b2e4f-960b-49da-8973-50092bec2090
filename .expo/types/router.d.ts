/* eslint-disable */
import * as Router from 'expo-router';

export * from 'expo-router';

declare module 'expo-router' {
  export namespace ExpoRouter {
    export interface __routes<T extends string | object = string> {
      hrefInputParams: { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/`; params?: Router.UnknownInputParams; } | { pathname: `/login`; params?: Router.UnknownInputParams; } | { pathname: `/modal`; params?: Router.UnknownInputParams; } | { pathname: `/welcome-new`; params?: Router.UnknownInputParams; } | { pathname: `/welcome`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(document-stack)'}/document-editor` | `/document-editor`; params?: Router.UnknownInputParams; } | { pathname: `${'/(document-stack)'}/document-viewer` | `/document-viewer`; params?: Router.UnknownInputParams; } | { pathname: `${'/(document-stack)'}/text-recognition` | `/text-recognition`; params?: Router.UnknownInputParams; } | { pathname: `${'/(drawer)'}/settings` | `/settings`; params?: Router.UnknownInputParams; } | { pathname: `${'/(knowledge)'}/cards` | `/cards`; params?: Router.UnknownInputParams; } | { pathname: `${'/(knowledge)'}/graph` | `/graph`; params?: Router.UnknownInputParams; } | { pathname: `${'/(knowledge)'}/share` | `/share`; params?: Router.UnknownInputParams; } | { pathname: `${'/(knowledge)'}/bones/CardsList` | `/bones/CardsList`; params?: Router.UnknownInputParams; } | { pathname: `${'/(knowledge)'}/bones/KnowledgeCard` | `/bones/KnowledgeCard`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/home` | `/home`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/library` | `/library`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/profile` | `/profile`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/search` | `/search`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/bones/CTAButtons` | `/bones/CTAButtons`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/bones/CardsList` | `/bones/CardsList`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/bones/FeatureCard` | `/bones/FeatureCard`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/bones/FeatureGrid` | `/bones/FeatureGrid`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/bones/Guideline` | `/bones/Guideline`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/bones/HeroHeading` | `/bones/HeroHeading`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/bones/HeroLogo` | `/bones/HeroLogo`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/bones/HeroSection` | `/bones/HeroSection`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/bones/HomeBackground` | `/bones/HomeBackground`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/bones/HomeHeader` | `/bones/HomeHeader`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/bones/KnowledgeCard` | `/bones/KnowledgeCard`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/bones/StatsSection` | `/bones/StatsSection`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/bones/TabSelectorButtons` | `/bones/TabSelectorButtons`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/scan/camera-working` | `/scan/camera-working`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/scan/camera` | `/scan/camera`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/scan/gallery` | `/scan/gallery`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/scan/review` | `/scan/review`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/scan/upload` | `/scan/upload`; params?: Router.UnknownInputParams; } | { pathname: `${'/(workflows)'}/social-share` | `/social-share`; params?: Router.UnknownInputParams; } | { pathname: `${'/(workflows)'}/text-review` | `/text-review`; params?: Router.UnknownInputParams; };
      hrefOutputParams: { pathname: Router.RelativePathString, params?: Router.UnknownOutputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownOutputParams } | { pathname: `/`; params?: Router.UnknownOutputParams; } | { pathname: `/login`; params?: Router.UnknownOutputParams; } | { pathname: `/modal`; params?: Router.UnknownOutputParams; } | { pathname: `/welcome-new`; params?: Router.UnknownOutputParams; } | { pathname: `/welcome`; params?: Router.UnknownOutputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(document-stack)'}/document-editor` | `/document-editor`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(document-stack)'}/document-viewer` | `/document-viewer`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(document-stack)'}/text-recognition` | `/text-recognition`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(drawer)'}/settings` | `/settings`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(knowledge)'}/cards` | `/cards`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(knowledge)'}/graph` | `/graph`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(knowledge)'}/share` | `/share`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(knowledge)'}/bones/CardsList` | `/bones/CardsList`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(knowledge)'}/bones/KnowledgeCard` | `/bones/KnowledgeCard`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/home` | `/home`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/library` | `/library`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/profile` | `/profile`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/search` | `/search`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/bones/CTAButtons` | `/bones/CTAButtons`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/bones/CardsList` | `/bones/CardsList`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/bones/FeatureCard` | `/bones/FeatureCard`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/bones/FeatureGrid` | `/bones/FeatureGrid`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/bones/Guideline` | `/bones/Guideline`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/bones/HeroHeading` | `/bones/HeroHeading`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/bones/HeroLogo` | `/bones/HeroLogo`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/bones/HeroSection` | `/bones/HeroSection`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/bones/HomeBackground` | `/bones/HomeBackground`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/bones/HomeHeader` | `/bones/HomeHeader`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/bones/KnowledgeCard` | `/bones/KnowledgeCard`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/bones/StatsSection` | `/bones/StatsSection`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/bones/TabSelectorButtons` | `/bones/TabSelectorButtons`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/scan/camera-working` | `/scan/camera-working`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/scan/camera` | `/scan/camera`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/scan/gallery` | `/scan/gallery`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/scan/review` | `/scan/review`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/scan/upload` | `/scan/upload`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(workflows)'}/social-share` | `/social-share`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(workflows)'}/text-review` | `/text-review`; params?: Router.UnknownOutputParams; };
      href: Router.RelativePathString | Router.ExternalPathString | `/${`?${string}` | `#${string}` | ''}` | `/login${`?${string}` | `#${string}` | ''}` | `/modal${`?${string}` | `#${string}` | ''}` | `/welcome-new${`?${string}` | `#${string}` | ''}` | `/welcome${`?${string}` | `#${string}` | ''}` | `/_sitemap${`?${string}` | `#${string}` | ''}` | `${'/(document-stack)'}/document-editor${`?${string}` | `#${string}` | ''}` | `/document-editor${`?${string}` | `#${string}` | ''}` | `${'/(document-stack)'}/document-viewer${`?${string}` | `#${string}` | ''}` | `/document-viewer${`?${string}` | `#${string}` | ''}` | `${'/(document-stack)'}/text-recognition${`?${string}` | `#${string}` | ''}` | `/text-recognition${`?${string}` | `#${string}` | ''}` | `${'/(drawer)'}/settings${`?${string}` | `#${string}` | ''}` | `/settings${`?${string}` | `#${string}` | ''}` | `${'/(knowledge)'}/cards${`?${string}` | `#${string}` | ''}` | `/cards${`?${string}` | `#${string}` | ''}` | `${'/(knowledge)'}/graph${`?${string}` | `#${string}` | ''}` | `/graph${`?${string}` | `#${string}` | ''}` | `${'/(knowledge)'}/share${`?${string}` | `#${string}` | ''}` | `/share${`?${string}` | `#${string}` | ''}` | `${'/(knowledge)'}/bones/CardsList${`?${string}` | `#${string}` | ''}` | `/bones/CardsList${`?${string}` | `#${string}` | ''}` | `${'/(knowledge)'}/bones/KnowledgeCard${`?${string}` | `#${string}` | ''}` | `/bones/KnowledgeCard${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/home${`?${string}` | `#${string}` | ''}` | `/home${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/library${`?${string}` | `#${string}` | ''}` | `/library${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/profile${`?${string}` | `#${string}` | ''}` | `/profile${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/search${`?${string}` | `#${string}` | ''}` | `/search${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/bones/CTAButtons${`?${string}` | `#${string}` | ''}` | `/bones/CTAButtons${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/bones/CardsList${`?${string}` | `#${string}` | ''}` | `/bones/CardsList${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/bones/FeatureCard${`?${string}` | `#${string}` | ''}` | `/bones/FeatureCard${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/bones/FeatureGrid${`?${string}` | `#${string}` | ''}` | `/bones/FeatureGrid${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/bones/Guideline${`?${string}` | `#${string}` | ''}` | `/bones/Guideline${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/bones/HeroHeading${`?${string}` | `#${string}` | ''}` | `/bones/HeroHeading${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/bones/HeroLogo${`?${string}` | `#${string}` | ''}` | `/bones/HeroLogo${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/bones/HeroSection${`?${string}` | `#${string}` | ''}` | `/bones/HeroSection${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/bones/HomeBackground${`?${string}` | `#${string}` | ''}` | `/bones/HomeBackground${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/bones/HomeHeader${`?${string}` | `#${string}` | ''}` | `/bones/HomeHeader${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/bones/KnowledgeCard${`?${string}` | `#${string}` | ''}` | `/bones/KnowledgeCard${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/bones/StatsSection${`?${string}` | `#${string}` | ''}` | `/bones/StatsSection${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/bones/TabSelectorButtons${`?${string}` | `#${string}` | ''}` | `/bones/TabSelectorButtons${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/scan/camera-working${`?${string}` | `#${string}` | ''}` | `/scan/camera-working${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/scan/camera${`?${string}` | `#${string}` | ''}` | `/scan/camera${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/scan/gallery${`?${string}` | `#${string}` | ''}` | `/scan/gallery${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/scan/review${`?${string}` | `#${string}` | ''}` | `/scan/review${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/scan/upload${`?${string}` | `#${string}` | ''}` | `/scan/upload${`?${string}` | `#${string}` | ''}` | `${'/(workflows)'}/social-share${`?${string}` | `#${string}` | ''}` | `/social-share${`?${string}` | `#${string}` | ''}` | `${'/(workflows)'}/text-review${`?${string}` | `#${string}` | ''}` | `/text-review${`?${string}` | `#${string}` | ''}` | { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/`; params?: Router.UnknownInputParams; } | { pathname: `/login`; params?: Router.UnknownInputParams; } | { pathname: `/modal`; params?: Router.UnknownInputParams; } | { pathname: `/welcome-new`; params?: Router.UnknownInputParams; } | { pathname: `/welcome`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(document-stack)'}/document-editor` | `/document-editor`; params?: Router.UnknownInputParams; } | { pathname: `${'/(document-stack)'}/document-viewer` | `/document-viewer`; params?: Router.UnknownInputParams; } | { pathname: `${'/(document-stack)'}/text-recognition` | `/text-recognition`; params?: Router.UnknownInputParams; } | { pathname: `${'/(drawer)'}/settings` | `/settings`; params?: Router.UnknownInputParams; } | { pathname: `${'/(knowledge)'}/cards` | `/cards`; params?: Router.UnknownInputParams; } | { pathname: `${'/(knowledge)'}/graph` | `/graph`; params?: Router.UnknownInputParams; } | { pathname: `${'/(knowledge)'}/share` | `/share`; params?: Router.UnknownInputParams; } | { pathname: `${'/(knowledge)'}/bones/CardsList` | `/bones/CardsList`; params?: Router.UnknownInputParams; } | { pathname: `${'/(knowledge)'}/bones/KnowledgeCard` | `/bones/KnowledgeCard`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/home` | `/home`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/library` | `/library`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/profile` | `/profile`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/search` | `/search`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/bones/CTAButtons` | `/bones/CTAButtons`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/bones/CardsList` | `/bones/CardsList`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/bones/FeatureCard` | `/bones/FeatureCard`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/bones/FeatureGrid` | `/bones/FeatureGrid`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/bones/Guideline` | `/bones/Guideline`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/bones/HeroHeading` | `/bones/HeroHeading`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/bones/HeroLogo` | `/bones/HeroLogo`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/bones/HeroSection` | `/bones/HeroSection`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/bones/HomeBackground` | `/bones/HomeBackground`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/bones/HomeHeader` | `/bones/HomeHeader`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/bones/KnowledgeCard` | `/bones/KnowledgeCard`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/bones/StatsSection` | `/bones/StatsSection`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/bones/TabSelectorButtons` | `/bones/TabSelectorButtons`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/scan/camera-working` | `/scan/camera-working`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/scan/camera` | `/scan/camera`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/scan/gallery` | `/scan/gallery`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/scan/review` | `/scan/review`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/scan/upload` | `/scan/upload`; params?: Router.UnknownInputParams; } | { pathname: `${'/(workflows)'}/social-share` | `/social-share`; params?: Router.UnknownInputParams; } | { pathname: `${'/(workflows)'}/text-review` | `/text-review`; params?: Router.UnknownInputParams; };
    }
  }
}
