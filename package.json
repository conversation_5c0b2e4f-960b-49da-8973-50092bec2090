{"name": "expo-template", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start-with-logging": "expo start > running_outputs.log 2>&1", "start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "DARK_MODE=media expo run:android", "ios": "DARK_MODE=media expo run:ios", "web": "DARK_MODE=media expo start --web", "test": "jest --watchAll", "lint": "expo lint", "doc": "typedoc", "build:android": "eas build -p android --local", "build:ios": "eas build -p ios --local", "prepare": "husky"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@ai-sdk/openai": "^1.2.3", "@ai-sdk/openai-compatible": "^0.1.15", "@ai-sdk/react": "^1.1.22", "@dr.pogodin/react-native-fs": "^2.32.0", "@expo/html-elements": "^0.4.2", "@expo/vector-icons": "^14.0.2", "@gluestack-ui/accordion": "^1.0.14", "@gluestack-ui/actionsheet": "^0.2.53", "@gluestack-ui/alert-dialog": "^0.1.38", "@gluestack-ui/avatar": "^0.1.18", "@gluestack-ui/button": "^1.0.14", "@gluestack-ui/checkbox": "^0.1.39", "@gluestack-ui/divider": "^0.1.10", "@gluestack-ui/fab": "^0.1.28", "@gluestack-ui/icon": "^0.1.26", "@gluestack-ui/input": "^0.1.38", "@gluestack-ui/modal": "^0.1.41", "@gluestack-ui/nativewind-utils": "^1.0.26", "@gluestack-ui/overlay": "^0.1.22", "@gluestack-ui/popover": "^0.1.49", "@gluestack-ui/pressable": "^0.1.23", "@gluestack-ui/progress": "^0.1.18", "@gluestack-ui/radio": "^0.1.40", "@gluestack-ui/select": "^0.1.31", "@gluestack-ui/slider": "^0.1.32", "@gluestack-ui/spinner": "^0.1.15", "@gluestack-ui/switch": "^0.1.29", "@gluestack-ui/textarea": "^0.1.25", "@gluestack-ui/toast": "^1.0.9", "@legendapp/motion": "^2.4.0", "@pocketpalai/llama.rn": "^0.5.6-1", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-masked-view/masked-view": "0.3.2", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/drawer": "^7.1.1", "@react-navigation/native": "^7.0.14", "@stardazed/streams-text-encoding": "^1.0.2", "@ungap/structured-clone": "^1.3.0", "ai": "^4.1.58", "axios": "^1.7.9", "babel-plugin-module-resolver": "^5.0.2", "chat-formatter": "^0.3.4", "classnames": "^2.5.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dotenv": "^16.4.7", "expo": "~52.0.46", "expo-auth-session": "~6.0.3", "expo-blur": "~14.0.3", "expo-camera": "~16.0.18", "expo-clipboard": "~7.0.1", "expo-constants": "~17.0.8", "expo-crypto": "~14.0.2", "expo-device": "~7.0.3", "expo-font": "~13.0.4", "expo-haptics": "~14.0.1", "expo-image-manipulator": "~13.0.6", "expo-image-picker": "~16.0.6", "expo-linear-gradient": "~14.0.2", "expo-linking": "~7.0.5", "expo-localization": "~16.0.1", "expo-location": "~18.0.10", "expo-media-library": "~17.0.6", "expo-notifications": "~0.29.14", "expo-router": "~4.0.21", "expo-splash-screen": "~0.29.24", "expo-status-bar": "~2.0.1", "expo-symbols": "~0.2.2", "expo-system-ui": "~4.0.9", "expo-web-browser": "~14.0.2", "i18n-js": "^4.5.0", "lucide-react-native": "^0.475.0", "nativewind": "^4.1.23", "radash": "^12.1.0", "react": "18.3.1", "react-dom": "18.3.1", "react-native": "0.76.9", "react-native-css-interop": "^0.1.22", "react-native-device-info": "^14.0.4", "react-native-gesture-handler": "~2.20.2", "react-native-gifted-chat": "^2.8.0", "react-native-keyboard-controller": "^1.16.7", "react-native-maps": "1.18.0", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-svg": "15.8.0", "react-native-vector-icons": "^10.2.0", "react-native-vision-camera": "^4.7.0", "react-native-web": "~0.19.13", "react-native-webview": "^13.12.5", "swr": "^2.2.5", "tailwind-merge": "^3.0.1", "tailwindcss": "^3.4.17", "usehooks-ts": "^3.1.1", "zod": "^3.25.67", "zustand": "^5.0.2"}, "devDependencies": {"@babel/core": "^7.25.2", "@commitlint/cli": "^19.6.0", "@commitlint/config-conventional": "^19.6.0", "@testing-library/react-native": "^13.2.0", "@types/jest": "^29.5.12", "@types/react": "~18.3.12", "@types/react-native-vector-icons": "^6.4.18", "@types/react-test-renderer": "^18.3.0", "@types/ungap__structured-clone": "^1.2.0", "commitizen": "^4.3.1", "cz-conventional-changelog": "^3.3.0", "eas-cli": "^16.0.1", "husky": "^9.1.7", "jest": "^29.2.1", "jest-expo": "~52.0.6", "jscodeshift": "^0.15.2", "react-test-renderer": "18.3.1", "typedoc": "^0.27.3", "typescript": "^5.3.3"}, "private": true}