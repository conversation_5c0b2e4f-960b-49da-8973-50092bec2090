import React from 'react';
import { useRouter } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import AppwriteAuthScreen from '@/components/auth/AppwriteAuthScreen';
import { useAuth } from '@/lib/contexts/AuthContext';
import type { AuthUser } from '@/types/appwrite';

export default function SignupScreen() {
  const router = useRouter();
  const { isAuthenticated } = useAuth();

  // If already authenticated, redirect to home
  React.useEffect(() => {
    if (isAuthenticated) {
      router.replace('/(tabs)/home');
    }
  }, [isAuthenticated]);

  const handleAuthSuccess = (user: AuthUser) => {
    console.log('Account created successfully:', user.name);
    
    // Navigate to main app
    router.replace('/(tabs)/home');
  };

  return (
    <>
      <StatusBar style="auto" />
      <AppwriteAuthScreen 
        onAuthSuccess={handleAuthSuccess}
        initialMode="signup"
      />
    </>
  );
}
