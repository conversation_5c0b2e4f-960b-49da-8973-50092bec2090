import { Drawer } from 'expo-router/drawer';
import { useColorScheme } from 'react-native';
import { 
  SettingsIcon,
  HelpCircleIcon,
  InfoIcon,
  ShieldIcon,
  CloudIcon,
  BellIcon,
  StarIcon,
  UserIcon
} from 'lucide-react-native';

export default function DrawerLayout() {
  const colorScheme = useColorScheme();

  return (
    <Drawer
      screenOptions={{
        headerStyle: {
          backgroundColor: colorScheme === 'dark' ? 'rgba(31, 41, 55, 0.95)' : 'rgba(255, 255, 255, 0.95)',
        },
        headerTintColor: colorScheme === 'dark' ? '#F3F4F6' : '#111827',
        headerTitleStyle: {
          fontWeight: 'bold',
        },
        headerShadowVisible: false,
        headerTransparent: true,
        headerBlurEffect: 'regular',
        drawerStyle: {
          backgroundColor: colorScheme === 'dark' ? 'rgba(31, 41, 55, 0.95)' : 'rgba(255, 255, 255, 0.95)',
          width: 280,
        },
        drawerActiveTintColor: '#FF6B9D',
        drawerInactiveTintColor: colorScheme === 'dark' ? '#9CA3AF' : '#6B7280',
        drawerLabelStyle: {
          fontWeight: '600',
        },
      }}
    >
      <Drawer.Screen
        name="settings"
        options={{
          title: 'Settings',
          drawerIcon: ({ color, size }) => (
            <SettingsIcon size={size} color={color} />
          ),
        }}
      />
      <Drawer.Screen
        name="account"
        options={{
          title: 'Account',
          drawerIcon: ({ color, size }) => (
            <UserIcon size={size} color={color} />
          ),
        }}
      />
      <Drawer.Screen
        name="premium"
        options={{
          title: 'Premium Features',
          drawerIcon: ({ color, size }) => (
            <StarIcon size={size} color={color} />
          ),
        }}
      />
      <Drawer.Screen
        name="cloud-sync"
        options={{
          title: 'Cloud Sync',
          drawerIcon: ({ color, size }) => (
            <CloudIcon size={size} color={color} />
          ),
        }}
      />
      <Drawer.Screen
        name="notifications"
        options={{
          title: 'Notifications',
          drawerIcon: ({ color, size }) => (
            <BellIcon size={size} color={color} />
          ),
        }}
      />
      <Drawer.Screen
        name="privacy"
        options={{
          title: 'Privacy & Security',
          drawerIcon: ({ color, size }) => (
            <ShieldIcon size={size} color={color} />
          ),
        }}
      />
      <Drawer.Screen
        name="help"
        options={{
          title: 'Help & Support',
          drawerIcon: ({ color, size }) => (
            <HelpCircleIcon size={size} color={color} />
          ),
        }}
      />
      <Drawer.Screen
        name="about"
        options={{
          title: 'About',
          drawerIcon: ({ color, size }) => (
            <InfoIcon size={size} color={color} />
          ),
        }}
      />
    </Drawer>
  );
}