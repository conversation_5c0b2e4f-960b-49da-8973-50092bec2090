import React, { useState } from 'react';
import { ScrollView, View } from 'react-native';
import { Text } from '../../components/ui/text';
import { VStack } from '../../components/ui/vstack';
import { HStack } from '../../components/ui/hstack';
import { Box } from '../../components/ui/box';
import { Button, ButtonText } from '../../components/ui/button';
import { Switch } from '../../components/ui/switch';
import { useRouter } from 'expo-router';
import { 
  SettingsIcon,
  CameraIcon,
  CloudIcon,
  ShieldIcon
} from 'lucide-react-native';

export default function SettingsScreen() {
  const router = useRouter();
  
  const [settings, setSettings] = useState({
    autoEnhance: true,
    scanQuality: 'high' as const,
    autoSave: true,
    darkMode: false,
    notifications: true,
    cloudSync: true,
    autoBackup: true,
    wifiOnly: false
  });

  const handleSettingChange = (key: string, value: any) => {
    setSettings(prev => ({ ...prev, [key]: value }));
  };

  return (
    <ScrollView className="flex-1 bg-background-0">
      <VStack space="lg" className="p-6 pb-24">
        {/* Scan Settings */}
        <VStack space="md">
          <HStack className="items-center space-x-3">
            <Box className="w-10 h-10 justify-center items-center rounded-full bg-candyPink/20">
              <CameraIcon size={20} color="#FF6B9D" />
            </Box>
            <Text color="primary" size="lg" style={{ fontWeight: 'bold' }}>
              Scan Settings
            </Text>
          </HStack>
          
          <VStack space="sm">
            <Box className="bg-glass-bg-card border border-glass-border-secondary backdrop-blur-md rounded-xl p-4">
              <HStack className="justify-between items-center">
                <VStack space="xs" className="flex-1">
                  <Text color="primary" size="md" style={{ fontWeight: '600' }}>
                    Auto Enhancement
                  </Text>
                  <Text color="secondary" size="sm">
                    Automatically enhance scanned images
                  </Text>
                </VStack>
                <Switch
                  value={settings.autoEnhance}
                  onValueChange={(value) => handleSettingChange('autoEnhance', value)}
                />
              </HStack>
            </Box>

            <Box className="bg-glass-bg-card border border-glass-border-secondary backdrop-blur-md rounded-xl p-4">
              <HStack className="justify-between items-center">
                <VStack space="xs" className="flex-1">
                  <Text color="primary" size="md" style={{ fontWeight: '600' }}>
                    Auto Save
                  </Text>
                  <Text color="secondary" size="sm">
                    Automatically save scanned documents
                  </Text>
                </VStack>
                <Switch
                  value={settings.autoSave}
                  onValueChange={(value) => handleSettingChange('autoSave', value)}
                />
              </HStack>
            </Box>
          </VStack>
        </VStack>

        {/* App Settings */}
        <VStack space="md">
          <HStack className="items-center space-x-3">
            <Box className="w-10 h-10 justify-center items-center rounded-full bg-candyPurple/20">
              <SettingsIcon size={20} color="#A855F7" />
            </Box>
            <Text color="primary" size="lg" style={{ fontWeight: 'bold' }}>
              App Settings
            </Text>
          </HStack>
          
          <VStack space="sm">
            <Box className="bg-glass-bg-card border border-glass-border-secondary backdrop-blur-md rounded-xl p-4">
              <HStack className="justify-between items-center">
                <VStack space="xs" className="flex-1">
                  <Text color="primary" size="md" style={{ fontWeight: '600' }}>
                    Dark Mode
                  </Text>
                  <Text color="secondary" size="sm">
                    Use dark theme
                  </Text>
                </VStack>
                <Switch
                  value={settings.darkMode}
                  onValueChange={(value) => handleSettingChange('darkMode', value)}
                />
              </HStack>
            </Box>

            <Box className="bg-glass-bg-card border border-glass-border-secondary backdrop-blur-md rounded-xl p-4">
              <HStack className="justify-between items-center">
                <VStack space="xs" className="flex-1">
                  <Text color="primary" size="md" style={{ fontWeight: '600' }}>
                    Notifications
                  </Text>
                  <Text color="secondary" size="sm">
                    Receive app notifications
                  </Text>
                </VStack>
                <Switch
                  value={settings.notifications}
                  onValueChange={(value) => handleSettingChange('notifications', value)}
                />
              </HStack>
            </Box>
          </VStack>
        </VStack>

        {/* Cloud Settings */}
        <VStack space="md">
          <HStack className="items-center space-x-3">
            <Box className="w-10 h-10 justify-center items-center rounded-full bg-candyBlue/20">
              <CloudIcon size={20} color="#3B82F6" />
            </Box>
            <Text color="primary" size="lg" style={{ fontWeight: 'bold' }}>
              Cloud & Sync
            </Text>
          </HStack>
          
          <VStack space="sm">
            <Box className="bg-glass-bg-card border border-glass-border-secondary backdrop-blur-md rounded-xl p-4">
              <HStack className="justify-between items-center">
                <VStack space="xs" className="flex-1">
                  <Text color="primary" size="md" style={{ fontWeight: '600' }}>
                    Cloud Sync
                  </Text>
                  <Text color="secondary" size="sm">
                    Sync documents across devices
                  </Text>
                </VStack>
                <Switch
                  value={settings.cloudSync}
                  onValueChange={(value) => handleSettingChange('cloudSync', value)}
                />
              </HStack>
            </Box>

            <Box className="bg-glass-bg-card border border-glass-border-secondary backdrop-blur-md rounded-xl p-4">
              <HStack className="justify-between items-center">
                <VStack space="xs" className="flex-1">
                  <Text color="primary" size="md" style={{ fontWeight: '600' }}>
                    Auto Backup
                  </Text>
                  <Text color="secondary" size="sm">
                    Automatically backup to cloud
                  </Text>
                </VStack>
                <Switch
                  value={settings.autoBackup}
                  onValueChange={(value) => handleSettingChange('autoBackup', value)}
                />
              </HStack>
            </Box>
          </VStack>
        </VStack>
      </VStack>
    </ScrollView>
  );
}