import React from 'react';
import { ScrollView, StyleSheet } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Box } from '@/components/ui/box';
import { HomeBackground } from '@/bones/HomeBackground';
import { HomeHeader } from '@/bones/HomeHeader';
import { HeroSection } from '@/bones/HeroSection';

export default function Home() {
  return (
    <Box className="flex-1 relative">
      <HomeBackground />

      <SafeAreaView style={styles.safeAreaContainer}>
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          <HomeHeader />
          <HeroSection />
        </ScrollView>
      </SafeAreaView>
    </Box>
  );
};

const styles = StyleSheet.create({
  safeAreaContainer: {
    flex: 1,
    position: 'relative',
    zIndex: 9,
  },
  scrollContent: {
    flexGrow: 1,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: '100%',
  },
});
