import { createOpenAICompatible } from '@ai-sdk/openai-compatible';
import { streamText, smoothStream, type Message, type CoreMessage } from 'ai';

const TEMP_PROVIDER_API_KEY = 'kala-0719';
const TEMP_MODEL_NAME = 'random';
const TEMP_API_BASE_URL = 'http://127.0.0.1:9978/v1/models';

const CROS_HEADERS = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
  "Access-Control-Allow-Headers": "Content-Type, Authorization",
  "Content-Type": "application/octet-stream",
};

const provider = createOpenAICompatible({
  name: 'simple-one-endpoint',
  apiKey: TEMP_PROVIDER_API_KEY,
  baseURL: TEMP_API_BASE_URL,
});

export const GET = async (req: Request) => {
  return Response.json({hello: 'world!'});
};

export const POST = async (req: Request) => {
  const { messages } = await req.json();

  const result = streamText({
    model: provider(TEMP_MODEL_NAME),
    messages,
  });

  return result.toDataStreamResponse({
    headers: {
      'Content-Type': 'application/octet-stream',
    }
  });
}

export const OPTIONS = async () => {
  return Response.json(null, { headers: CROS_HEADERS });
}

