import React, { useState, useMemo } from 'react';
import { ScrollView, View, Pressable } from 'react-native';
import { Text } from '../../components/ui/text';
import { VStack } from '../../components/ui/vstack';
import { HStack } from '../../components/ui/hstack';
import { Box } from '../../components/ui/box';
import { Button, ButtonText } from '../../components/ui/button';
import { useRouter } from 'expo-router';
import { 
  DocumentCard,
  FolderGrid,
  SearchBar,
  FilterPanel
} from '../../components/learni-scan';
import { 
  FolderIcon, 
  FileTextIcon,
  ImageIcon,
  FilterIcon,
  GridIcon,
  ListIcon,
  PlusIcon,
  BriefcaseIcon,
  UserIcon,
  ArchiveIcon
} from 'lucide-react-native';

type ViewMode = 'documents' | 'folders';
type LayoutMode = 'grid' | 'list';

function LibraryScreen() {
  const router = useRouter();
  const [viewMode, setViewMode] = useState<ViewMode>('documents');
  const [layoutMode, setLayoutMode] = useState<LayoutMode>('grid');
  const [showFilters, setShowFilters] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState({
    documentType: 'all' as const,
    dateRange: 'all' as const,
    tags: [] as string[],
    folder: undefined as string | undefined,
    confidence: 'all' as const,
    ocrStatus: 'all' as const,
    isFavorite: false,
    isShared: false,
    sortBy: 'date' as const,
    sortOrder: 'desc' as const
  });

  // Mock document data
  const [documents] = useState([
    {
      id: 'doc-001',
      title: 'Meeting Notes - Q4 Planning',
      description: 'Strategic planning session notes from December 15th',
      thumbnailUri: undefined,
      type: 'document' as const,
      size: 2048576, // 2MB
      createdAt: new Date('2024-12-15'),
      modifiedAt: new Date('2024-12-16'),
      tags: ['meeting', 'planning', 'q4', 'strategy'],
      folder: 'Work Documents',
      isFavorite: true,
      confidence: 92,
      pageCount: 5,
      ocrStatus: 'completed' as const,
      metadata: {
        language: 'en',
        wordCount: 1247,
        resolution: '300dpi',
        colorSpace: 'sRGB'
      }
    },
    {
      id: 'doc-002',
      title: 'Invoice Template',
      description: 'Standard invoice template for client billing',
      thumbnailUri: undefined,
      type: 'pdf' as const,
      size: 1024768, // 1MB
      createdAt: new Date('2024-12-10'),
      modifiedAt: new Date('2024-12-10'),
      tags: ['invoice', 'template', 'billing'],
      folder: 'Templates',
      isFavorite: false,
      confidence: 88,
      pageCount: 2,
      ocrStatus: 'completed' as const,
      metadata: {
        language: 'en',
        wordCount: 456,
        resolution: '300dpi',
        colorSpace: 'sRGB'
      }
    },
    {
      id: 'doc-003',
      title: 'Contract Draft',
      description: 'Service agreement contract draft',
      thumbnailUri: undefined,
      type: 'document' as const,
      size: 3145728, // 3MB
      createdAt: new Date('2024-12-08'),
      modifiedAt: new Date('2024-12-12'),
      tags: ['contract', 'legal', 'draft'],
      folder: 'Legal Documents',
      isFavorite: true,
      confidence: 95,
      pageCount: 8,
      ocrStatus: 'completed' as const,
      metadata: {
        language: 'en',
        wordCount: 2134,
        resolution: '300dpi',
        colorSpace: 'sRGB'
      }
    }
  ]);

  // Mock folder data
  const [folders] = useState([
    {
      id: 'folder-001',
      name: 'Work Documents',
      description: 'Professional documents and reports',
      color: '#FF6B9D',
      documentCount: 24,
      totalSize: 52428800, // 50MB
      createdAt: new Date('2024-01-15'),
      modifiedAt: new Date('2024-12-16'),
      tags: ['work', 'professional', 'reports'],
      isArchived: false,
      isFavorite: true,
      isShared: false,
      recentActivity: {
        type: 'added' as const,
        count: 3,
        timestamp: new Date()
      },
      metadata: {
        ocrCompleted: 22,
        averageConfidence: 87,
        languages: ['en', 'es']
      }
    }
  ]);

  // Filter documents based on search and filters
  const filteredDocuments = useMemo(() => {
    return documents.filter(doc => {
      // Apply search query
      if (searchQuery && !doc.title.toLowerCase().includes(searchQuery.toLowerCase())) {
        return false;
      }
      
      // Apply filters
      if (filters.documentType !== 'all' && doc.type !== filters.documentType) {
        return false;
      }
      
      if (filters.tags && filters.tags.length > 0 && !filters.tags.some(tag => doc.tags.includes(tag))) {
        return false;
      }
      
      if (filters.isFavorite && !doc.isFavorite) {
        return false;
      }
      
      return true;
    });
  }, [documents, searchQuery, filters]);

  const handleSearch = (query: string, searchFilters: any) => {
    setSearchQuery(query);
  };

  const handleDocumentAction = (action: string, document: any) => {
    console.log(`${action} document:`, document.title);
  };

  const handleFolderAction = (action: string, folder: any) => {
    console.log(`${action} folder:`, folder.name);
  };

  return (
    <View className="flex-1 bg-background-0">
      {/* Search Header */}
      <Box className="bg-glass-bg-primary border-b border-glass-border-primary backdrop-blur-md pt-16 pb-4 px-4">
        <SearchBar
          onSearch={handleSearch}
          onFilterChange={setFilters}
          placeholder="Search documents, text, or tags..."
          recentSearches={['meeting notes', 'invoices', 'contracts']}
          suggestions={[
            { id: '1', text: 'meeting', type: 'tag', count: 15 },
            { id: '2', text: 'Work Documents', type: 'folder', count: 24 },
            { id: '3', text: 'Q4 planning', type: 'document', count: 1 }
          ]}
          showFilters={true}
          showRecentSearches={true}
          showSuggestions={true}
          variant="glass"
          size="md"
        />
      </Box>

      {/* View Controls */}
      <Box className="bg-glass-bg-primary border-b border-glass-border-primary backdrop-blur-md px-4 py-3">
        <HStack className="justify-between items-center">
          <HStack className="space-x-2">
            <Button
              action={viewMode === 'documents' ? 'candyPink' : 'glass'}
              variant={viewMode === 'documents' ? 'solid' : 'outline'}
              size="sm"
              onPress={() => setViewMode('documents')}
            >
              <ButtonText>Documents</ButtonText>
            </Button>
            <Button
              action={viewMode === 'folders' ? 'candyPurple' : 'glass'}
              variant={viewMode === 'folders' ? 'solid' : 'outline'}
              size="sm"
              onPress={() => setViewMode('folders')}
            >
              <ButtonText>Folders</ButtonText>
            </Button>
          </HStack>
          
          <HStack className="space-x-2">
            {viewMode === 'documents' && (
              <Button
                action="glass"
                variant="outline"
                size="sm"
                onPress={() => setLayoutMode(layoutMode === 'grid' ? 'list' : 'grid')}
              >
                {layoutMode === 'grid' ? (
                  <ListIcon size={16} color="#FFFFFF" />
                ) : (
                  <GridIcon size={16} color="#FFFFFF" />
                )}
              </Button>
            )}
            <Button
              action="glass"
              variant="outline"
              size="sm"
              onPress={() => setShowFilters(!showFilters)}
            >
              <FilterIcon size={16} color="#FFFFFF" />
            </Button>
          </HStack>
        </HStack>
      </Box>

      {/* Content Area */}
      <ScrollView className="flex-1 p-4">
        {viewMode === 'documents' ? (
          <VStack space="md">
            {filteredDocuments.map(document => (
              <DocumentCard
                key={document.id}
                document={document}
                onPress={(doc) => handleDocumentAction('view', doc)}
                onShare={(doc) => handleDocumentAction('share', doc)}
                onDownload={(doc) => handleDocumentAction('download', doc)}
                onEdit={(doc) => handleDocumentAction('edit', doc)}
                onDelete={(doc) => handleDocumentAction('delete', doc)}
                onFavorite={(doc) => handleDocumentAction('favorite', doc)}
                showActions={true}
                variant="glass"
                size="md"
                layout={layoutMode === 'grid' ? 'card' : 'list'}
              />
            ))}
            
            {filteredDocuments?.length === 0 && (
              <Box className="flex-1 justify-center items-center py-12">
                <VStack space="md" className="items-center">
                  <Box className="w-16 h-16 justify-center items-center rounded-full bg-glass-bg-secondary">
                    <FileTextIcon size={32} color="#9CA3AF" />
                  </Box>
                  <VStack space="xs" className="items-center">
                    <Text color="primary" size="lg" style={{ fontWeight: 'bold' }}>
                      No Documents Found
                    </Text>
                    <Text color="secondary" size="sm" className="text-center">
                      Try adjusting your search or filters
                    </Text>
                  </VStack>
                  <Button action="candyPink" size="sm" onPress={() => router.push('/(tabs)/scan')}>
                    <HStack className="items-center space-x-2">
                      <PlusIcon size={16} color="#FFFFFF" />
                      <ButtonText>Scan Document</ButtonText>
                    </HStack>
                  </Button>
                </VStack>
              </Box>
            )}
          </VStack>
        ) : (
          <FolderGrid
            folders={folders}
            onFolderPress={(folder) => handleFolderAction('open', folder)}
            onCreateFolder={() => console.log('Create folder')}
            onEditFolder={(folder) => handleFolderAction('edit', folder)}
            onDeleteFolder={(folder) => handleFolderAction('delete', folder)}
            onShareFolder={(folder) => console.log('Share folder:', folder.name)}
            onArchiveFolder={(folder) => console.log('Archive folder:', folder.name)}
            showActions={true}
            variant="glass"
            columns={2}
          />
        )}
      </ScrollView>
    </View>
  );
}

export default LibraryScreen;