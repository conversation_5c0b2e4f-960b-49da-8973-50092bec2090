import React, { useState } from 'react';
import { Pressable, StyleSheet, Alert, StatusBar, ScrollView } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { router, useLocalSearchParams } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import Animated, { 
  useSharedValue, 
  useAnimatedStyle, 
  withTiming,
  withSpring
} from 'react-native-reanimated';
import {
  ArrowLeftIcon,
  EditIcon,
  ShareIcon,
  VolumeXIcon,
  CopyIcon,
  FileTextIcon,
  MapIcon,
  MessageCircleIcon,
  StarIcon,
} from 'lucide-react-native';

// Import Gluestack UI Button components
import { Button, ButtonText, ButtonGroup } from '@/components/ui/button';
import { z } from 'zod';

// Import UI components following Rule #6
import { Box } from '@/components/ui/box';
import { Text } from '@/components/ui/text';
import { Badge, BadgeIcon, BadgeText } from '@/components/ui/badge';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';

// Custom Toggle Component using Gluestack UI Button
const ToggleButton = ({
  leftLabel,
  rightLabel,
  isRightSelected,
  onToggle
}: {
  leftLabel: string;
  rightLabel: string;
  isRightSelected: boolean;
  onToggle: () => void;
}) => {
  return (
    <ButtonGroup isAttached flexDirection="row">
      <Button
        variant={!isRightSelected ? "solid" : "outline"}
        size="sm"
        onPress={() => isRightSelected && onToggle()}
        className="rounded-none"
        style={[
          styles.toggleButtonLeft,
          !isRightSelected && styles.toggleButtonActive
        ]}
      >
        <ButtonText
          style={[
            styles.toggleButtonText,
            !isRightSelected && styles.toggleButtonTextActive
          ]}
        >
          {leftLabel}
        </ButtonText>
      </Button>
      <Button
        variant={isRightSelected ? "solid" : "outline"}
        size="sm"
        onPress={() => !isRightSelected && onToggle()}
        className="rounded-none"
        style={[
          styles.toggleButtonRight,
          isRightSelected && styles.toggleButtonActive
        ]}
      >
        <ButtonText
          style={[
            styles.toggleButtonText,
            isRightSelected && styles.toggleButtonTextActive
          ]}
        >
          {rightLabel}
        </ButtonText>
      </Button>
    </ButtonGroup>
  );
};

export default function ReviewScreen() {
  const insets = useSafeAreaInsets();
  // const params = useLocalSearchParams(); // Available for future use
  
  // Zod validation schemas following Rule #11
  const ReviewStateSchema = z.object({
    extractedText: z.string(),
    translatedText: z.string(),
    selectedLanguage: z.string(),
    showTranslation: z.boolean(),
    ocrConfidence: z.number().min(0).max(100),
    isTranslating: z.boolean(),
    highlightedWords: z.array(z.string()),
  });

  type ReviewState = z.infer<typeof ReviewStateSchema>;

  const [state, setState] = useState<ReviewState>({
    extractedText: `Machine learning is a subset of artificial intelligence (AI) that provides systems the ability to automatically learn and improve from experience without being explicitly programmed. Machine learning focuses on the development of computer programs that can access data and use it to learn for themselves.

The process of learning begins with observations or data, such as examples, direct experience, or instruction, in order to look for patterns in data and make better decisions in the future based on the examples that we provide. The primary aim is to allow the computers to learn automatically without human intervention or assistance and adjust actions accordingly.

Some machine learning methods include supervised learning, unsupervised learning, and reinforcement learning. Each method has its own approach to analyzing data and making predictions or decisions.`,
    translatedText: `El aprendizaje automático es un subconjunto de la inteligencia artificial (IA) que proporciona a los sistemas la capacidad de aprender automáticamente y mejorar a partir de la experiencia sin ser programados explícitamente.

El proceso de aprendizaje comienza con observaciones o datos, como ejemplos, experiencia directa o instrucción, para buscar patrones en los datos y tomar mejores decisiones en el futuro basándose en los ejemplos que proporcionamos.

Algunos métodos de aprendizaje automático incluyen aprendizaje supervisado, aprendizaje no supervisado y aprendizaje por refuerzo.`,
    selectedLanguage: '',
    showTranslation: false,
    ocrConfidence: 94,
    isTranslating: false,
    highlightedWords: ['Machine learning', 'supervised learning', 'unsupervised learning', 'reinforcement learning'],
  });

  // Animation values following Rule #8
  const confidenceProgress = useSharedValue(0);
  const translateButtonScale = useSharedValue(1);

  const confidenceAnimatedStyle = useAnimatedStyle(() => {
    return {
      width: `${confidenceProgress.value}%`,
    };
  });

  const translateButtonAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: translateButtonScale.value }],
    };
  });

  // Initialize confidence animation
  React.useEffect(() => {
    confidenceProgress.value = withTiming(state.ocrConfidence, { duration: 1000 });
  }, [state.ocrConfidence]);

  // Translation functionality following Rule #12
  const handleTranslate = async () => {
    if (!state.selectedLanguage) {
      Alert.alert('Language Required', 'Please select a language to translate to.');
      return;
    }

    try {
      setState(prev => ({ ...prev, isTranslating: true }));
      translateButtonScale.value = withSpring(0.95);
      
      // Simulate translation API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      setState(prev => ({ ...prev, isTranslating: false }));
      translateButtonScale.value = withSpring(1);
      
      Alert.alert('Translation Complete', 'Text has been translated successfully!');
    } catch (error) {
      console.error('Translation error:', error);
      Alert.alert('Translation Error', 'Failed to translate text. Please try again.');
      setState(prev => ({ ...prev, isTranslating: false }));
      translateButtonScale.value = withSpring(1);
    }
  };

  const handleCopyText = async () => {
    // const textToCopy = state.showTranslation ? state.translatedText : state.extractedText;
    // In a real app, you would use Clipboard from @react-native-clipboard/clipboard
    Alert.alert('Copied', 'Text copied to clipboard!');
  };

  const handleReadAloud = () => {
    // In a real app, you would use expo-speech
    Alert.alert('Read Aloud', 'Text-to-speech functionality would start here.');
  };

  const handleGenerateKnowledgeCards = () => {
    Alert.alert('Knowledge Cards', 'Generating knowledge cards from extracted text...');
  };

  const handleCreateLearningPath = () => {
    Alert.alert('Learning Path', 'Creating personalized learning path...');
  };

  const handleStartAIChat = () => {
    Alert.alert('AI Chat', 'Starting AI chat about this content...');
  };

  const renderHighlightedText = (text: string) => {
    const parts = text.split(/(Machine learning|supervised learning|unsupervised learning|reinforcement learning)/gi);
    
    return parts.map((part, index) => {
      const isHighlighted = state.highlightedWords.some(word => 
        part.toLowerCase() === word.toLowerCase()
      );
      
      if (isHighlighted) {
        return (
          <Text key={index} className="bg-pink-400/30 px-1 py-0.5 rounded text-white">
            {part}
          </Text>
        );
      }
      return part;
    });
  };

  return (
    <LinearGradient
      colors={['#FF6B9D', '#A855F7', '#3B82F6', '#06B6D4']}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
      style={{ flex: 1 }}
    >
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />
      
      {/* Header */}
      <Box 
        className="flex-row justify-between items-center px-5 py-4"
        style={{ paddingTop: insets.top + 16 }}
      >
        <HStack className="items-center space-x-4">
          <Pressable onPress={() => router.back()}>
            <Box className="p-2 rounded-lg bg-white/10 backdrop-blur-md" style={styles.glassButton}>
              <ArrowLeftIcon size={24} color="#FFFFFF" />
            </Box>
          </Pressable>
          <Text className="text-white text-xl font-bold">Extracted Text</Text>
        </HStack>
        
        <HStack className="space-x-2">
          <Pressable onPress={() => Alert.alert('Edit', 'Edit functionality')}>
            <Box className="p-2 rounded-lg bg-white/10 backdrop-blur-md" style={styles.glassButton}>
              <EditIcon size={20} color="#FFFFFF" />
            </Box>
          </Pressable>
          <Pressable onPress={() => Alert.alert('Share', 'Share functionality')}>
            <Box className="p-2 rounded-lg bg-white/10 backdrop-blur-md" style={styles.glassButton}>
              <ShareIcon size={20} color="#FFFFFF" />
            </Box>
          </Pressable>
        </HStack>
      </Box>

      <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
        {/* OCR Confidence Indicator */}
        <Box className="px-5 mb-6">
          <Box className="p-5 rounded-xl bg-white/10 backdrop-blur-md" style={styles.glassCard}>
            <HStack className="justify-between items-center mb-3">
              <Text className="text-white text-base font-medium">OCR Confidence</Text>
              <Text className="text-green-400 text-base font-semibold">{state.ocrConfidence}%</Text>
            </HStack>
            <Box className="h-2 bg-white/20 rounded-full overflow-hidden">
              <Animated.View
                style={[
                  {
                    height: '100%',
                    backgroundColor: state.ocrConfidence >= 80 ? '#10B981' : state.ocrConfidence >= 60 ? '#F59E0B' : '#EF4444',
                    borderRadius: 4,
                  },
                  confidenceAnimatedStyle
                ]}
              />
            </Box>
          </Box>
        </Box>

        {/* Translation Controls */}
        <Box className="px-5 mb-6">
          <Box className="p-5 rounded-xl bg-white/10 backdrop-blur-md" style={styles.glassCard}>
            <VStack className="space-y-5">
              <VStack className="space-y-3">
                <Text className="text-white text-base font-medium">Translate to:</Text>
                <HStack className="items-stretch">
                  <Box className="flex-1">
                    <Pressable
                      onPress={() => {
                        Alert.alert(
                          'Select Language',
                          'Choose a language',
                          [
                            { text: 'Spanish', onPress: () => setState(prev => ({ ...prev, selectedLanguage: 'Spanish' })) },
                            { text: 'French', onPress: () => setState(prev => ({ ...prev, selectedLanguage: 'French' })) },
                            { text: 'German', onPress: () => setState(prev => ({ ...prev, selectedLanguage: 'German' })) },
                            { text: 'Cancel', style: 'cancel' }
                          ]
                        );
                      }}
                      style={[styles.languageSelector]}
                    >
                      <Text className="text-white text-base" style={{ lineHeight: 20 }}>
                        {state.selectedLanguage || 'Select Language'}
                      </Text>
                    </Pressable>
                  </Box>
                  <Animated.View style={translateButtonAnimatedStyle}>
                    <Pressable
                      onPress={handleTranslate}
                      disabled={state.isTranslating}
                      style={[styles.translateButton, styles.translateButtonConnected]}
                    >
                      <Text className="text-white text-base font-semibold" style={{ lineHeight: 20 }}>
                        {state.isTranslating ? 'Translating...' : 'Translate'}
                      </Text>
                    </Pressable>
                  </Animated.View>
                </HStack>
              </VStack>

              <HStack className="items-center justify-center py-4 px-6">
                <ToggleButton
                  leftLabel="Original"
                  rightLabel="Translation"
                  isRightSelected={state.showTranslation}
                  onToggle={() => setState(prev => ({ ...prev, showTranslation: !prev.showTranslation }))}
                />
              </HStack>
            </VStack>
          </Box>
        </Box>

        {/* Main Content */}
        <Box className="px-5 mb-8">
          <Box className="p-6 rounded-xl bg-white/10 backdrop-blur-md" style={styles.glassCard}>
            <HStack className="justify-between items-center mb-5">
              <Text className="text-white text-lg font-semibold">
                {state.showTranslation ? `${state.selectedLanguage} Translation` : 'Original Text'}
              </Text>
              <HStack className="space-x-3">
                {!state.showTranslation || (
                  <Badge size="lg" variant="solid" action="muted">
                    <BadgeText>Translated</BadgeText>
                    <BadgeIcon as={StarIcon} className="ml-2" />
                  </Badge>
                )}
                <Pressable onPress={handleReadAloud}>
                  <Box className="p-3 rounded-lg bg-white/10" style={styles.glassButton}>
                    <VolumeXIcon size={18} color="#FFFFFF" />
                  </Box>
                </Pressable>
                <Pressable onPress={handleCopyText}>
                  <Box className="p-3 rounded-lg bg-white/10" style={styles.glassButton}>
                    <CopyIcon size={18} color="#FFFFFF" />
                  </Box>
                </Pressable>
              </HStack>
            </HStack>

            <Box className="leading-relaxed">
              <Text className="text-white/90 text-base leading-8">
                {state.showTranslation ? state.translatedText : renderHighlightedText(state.extractedText)}
              </Text>
            </Box>
          </Box>
        </Box>

        {/* Action Buttons */}
        <Box className="px-5 pb-8" style={{ paddingBottom: insets.bottom + 32 }}>
          <VStack className="space-y-4">
            <Pressable
              onPress={handleGenerateKnowledgeCards}
              className="py-4 px-6 rounded-xl bg-pink-500 mx-1"
              style={[styles.actionButton, { marginBottom: 8 }]}
            >
              <HStack className="items-center justify-center space-x-2">
                <Text className="text-white text-base font-semibold">Generate Knowledge Cards</Text>
                <FileTextIcon size={20} color="#FFFFFF" />
              </HStack>
            </Pressable>

            <Pressable
              onPress={handleCreateLearningPath}
              className="py-4 px-6 rounded-xl bg-blue-500 mx-1"
              style={[styles.actionButton, { marginBottom: 8 }]}
            >
              <HStack className="items-center justify-center space-x-2">
                <Text className="text-white text-base font-semibold">Create Learning Path</Text>
                <MapIcon size={20} color="#FFFFFF" />
              </HStack>
            </Pressable>

            <Pressable
              onPress={handleStartAIChat}
              className="py-4 px-6 rounded-xl bg-white/10 backdrop-blur-md mx-1"
              style={[styles.glassButton, { marginBottom: 8 }]}
            >
              <HStack className="items-center justify-center space-x-2">
                <Text className="text-white text-base font-semibold">Start AI Chat</Text>
                <MessageCircleIcon size={20} color="#FFFFFF" />
              </HStack>
            </Pressable>
          </VStack>
        </Box>
      </ScrollView>
    </LinearGradient>
  );
}

// StyleSheet following Rule #6 (prefer StyleSheet.create for static styles)
const styles = StyleSheet.create({
  glassButton: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  glassCard: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
  },
  languageSelector: {
    height: 48,
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderTopLeftRadius: 8,
    borderBottomLeftRadius: 8,
    borderTopRightRadius: 0,
    borderBottomRightRadius: 0,
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  translateButton: {
    shadowColor: '#A855F7',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 4,
  },
  translateButtonConnected: {
    height: 48,
    paddingVertical: 12,
    paddingHorizontal: 20,
    backgroundColor: '#A855F7',
    borderTopLeftRadius: 0,
    borderBottomLeftRadius: 0,
    borderTopRightRadius: 8,
    borderBottomRightRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  toggleButtonLeft: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderTopLeftRadius: 6,
    borderBottomLeftRadius: 6,
    borderTopRightRadius: 0,
    borderBottomRightRadius: 0,
    borderRightWidth: 0,
    paddingHorizontal: 14,
    paddingVertical: 6,
    minWidth: 75,
  },
  toggleButtonRight: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderTopLeftRadius: 0,
    borderBottomLeftRadius: 0,
    borderTopRightRadius: 6,
    borderBottomRightRadius: 6,
    borderLeftWidth: 0,
    paddingHorizontal: 14,
    paddingVertical: 6,
    minWidth: 75,
  },
  toggleButtonActive: {
    backgroundColor: '#FF6B9D',
  },
  toggleButtonText: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: 14,
    fontWeight: '500',
  },
  toggleButtonTextActive: {
    color: '#FFFFFF',
    fontWeight: '600',
  },
  actionButton: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
});