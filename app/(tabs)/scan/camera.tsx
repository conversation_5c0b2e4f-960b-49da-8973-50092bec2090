import React, { useState, useRef, useEffect } from 'react';
import { Pressable, StyleSheet, StatusBar, Alert } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import { CameraView, CameraType, useCameraPermissions } from 'expo-camera';
import Animated, { 
  useSharedValue, 
  useAnimatedStyle, 
  withRepeat, 
  withTiming, 
  withSpring,
  interpolate,
  Easing
} from 'react-native-reanimated';
import { 
  ZapIcon,
  SettingsIcon,
  CameraIcon,
  RotateCcwIcon,
  ImageIcon
} from 'lucide-react-native';
import { z } from 'zod';

// Import UI components following Rule #6
import { Box } from '@/components/ui/box';
import { Text } from '@/components/ui/text';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Guideline } from '../bones/Guideline';
import { TabSelectorButtons, TabType } from '../bones/TabSelectorButtons';

const CameraStateSchema = z.object({
  facing: z.enum(['back', 'front']),
  flash: z.enum(['off', 'on', 'auto']),
  isCapturing: z.boolean(),
  guidelineState: z.enum(['scanning', 'captured', 'processing']),
  capturedPhoto: z.string().nullable(),
});

type ICameraState = z.infer<typeof CameraStateSchema>;

// Document frame component with corner indicators
const DocumentFrame: React.FC<{ isScanning: boolean }> = ({ isScanning }) => {
  const scanLinePosition = useSharedValue(0);
  const cornerScale = useSharedValue(1);

  // Scanning animation following Rule #8
  useEffect(() => {
    if (isScanning) {
      scanLinePosition.value = withRepeat(
        withTiming(1, { duration: 2000, easing: Easing.inOut(Easing.ease) }),
        -1,
        true
      );
      cornerScale.value = withRepeat(
        withSpring(1.1, { damping: 15, stiffness: 200 }),
        -1,
        true
      );
    } else {
      scanLinePosition.value = withTiming(0, { duration: 300 });
      cornerScale.value = withSpring(1);
    }
  }, [isScanning]);

  const scanLineStyle = useAnimatedStyle(() => {
    const translateY = interpolate(
      scanLinePosition.value,
      [0, 1],
      [0, 200] // Frame height - scan line height
    );
    
    return {
      transform: [{ translateY }],
      opacity: isScanning ? 0.8 : 0,
    };
  });

  const cornerStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: cornerScale.value }],
    };
  });

  return (
    <Box className="relative">
      {/* Main viewfinder frame with glass morphism */}
      <Box 
        className="w-80 h-52 border-2 border-dashed border-white/60 rounded-2xl bg-black/20 backdrop-blur-md"
        style={styles.glassFrame}
      >
        {/* Corner indicators */}
        <Animated.View style={[styles.cornerTopLeft, cornerStyle]}>
          <Box className="w-6 h-6 border-l-4 border-t-4 border-white/80 rounded-tl-lg" />
        </Animated.View>
        <Animated.View style={[styles.cornerTopRight, cornerStyle]}>
          <Box className="w-6 h-6 border-r-4 border-t-4 border-white/80 rounded-tr-lg" />
        </Animated.View>
        <Animated.View style={[styles.cornerBottomLeft, cornerStyle]}>
          <Box className="w-6 h-6 border-l-4 border-b-4 border-white/80 rounded-bl-lg" />
        </Animated.View>
        <Animated.View style={[styles.cornerBottomRight, cornerStyle]}>
          <Box className="w-6 h-6 border-r-4 border-b-4 border-white/80 rounded-br-lg" />
        </Animated.View>

        {/* Scanning line animation */}
        <Animated.View style={[styles.scanLine, scanLineStyle]}>
          <LinearGradient
            colors={['transparent', '#FF6B9D', 'transparent']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
            style={styles.scanLineGradient}
          />
        </Animated.View>

        {/* Center content */}
        <VStack className="flex-1 justify-center items-center p-6">
          <CameraIcon size={32} color="#FFFFFF" />
          <Text className="text-white text-center mt-2">
            Position document within frame
          </Text>
          <Text className="text-white/70 text-sm text-center mt-1">
            Align corners for best results
          </Text>
        </VStack>
      </Box>
    </Box>
  );
};

// Using official expo-camera permissions hook

export default function CameraScreen() {
  const insets = useSafeAreaInsets();
  
  // Direct state definitions (simple state)
  const [activeTab, setActiveTab] = useState<TabType>('camera');
  const [facing, setFacing] = useState<CameraType>('back');
  const [flash, setFlash] = useState<ICameraState['flash']>('off');
  const [isCapturing, setIsCapturing] = useState<ICameraState['isCapturing']>(false);
   const [capturedPhoto, setCapturedPhoto] = useState<ICameraState['capturedPhoto']>(null);
  const [guidelineState, setGuidelineState] = useState<ICameraState['guidelineState']>('scanning' as const);
  
  // Official expo-camera permissions hook
  const [permission, requestPermission] = useCameraPermissions();

  const cameraRef = useRef<any>(null);

  // Tab navigation handler
  const handleTabChange = (tab: TabType) => {
    setActiveTab(tab);
    if (tab === 'upload') {
      router.replace('./upload');
    } else if (tab === 'gallery') {
      router.replace('./gallery');
    }
  };

  // Camera control handlers
  const toggleFlash = () => {
    setFlash(prev => prev === 'off' ? 'on' : 'off');
  };

  const toggleCameraFacing = () => {
    setFacing(prev => prev === 'back' ? 'front' : 'back');
  };

  // Early return for permission handling (Rule #12)
  if (!permission) {
    return (
      <Box className="flex-1 justify-center items-center bg-black">
        <Text className="text-white">Requesting camera permission...</Text>
      </Box>
    );
  }

  if (!permission.granted) {
    return (
      <Box className="flex-1 justify-center items-center bg-black">
        <Text className="text-white text-center">
          Camera access is required to scan documents
        </Text>
        <Pressable onPress={requestPermission} style={{ marginTop: 16, padding: 12, backgroundColor: '#FF6B9D', borderRadius: 8 }}>
          <Text className="text-white text-center font-bold">Grant Permission</Text>
        </Pressable>
      </Box>
    );
  }

  const takePicture = async () => {
    if (!cameraRef.current || isCapturing) return;

    setIsCapturing(true);
    setGuidelineState('processing');
    
    try {
      const photo = await cameraRef.current.takePictureAsync({
        quality: 0.8,
        base64: false,
        skipProcessing: false,
      });
      
      // TODO: Process the captured image
      setCapturedPhoto(photo.uri);
      setGuidelineState('captured');
      console.log('Photo captured:', photo.uri);
      
    } catch (error) {
      setGuidelineState('scanning');
      console.error('Error taking picture:', error);
    } finally {
      setIsCapturing(false);
    }
  };

   const handleReviewNavigation = () => {
    if (!capturedPhoto) return;
    
    router.push({
      pathname: './review',
      params: {
        imageUri: capturedPhoto,
        source: 'camera',
        timestamp: Date.now().toString()
      }
    });
  };

  const handleRetake = () => {
    setCapturedPhoto(null);
    setGuidelineState('scanning');
  };

  return (
    <LinearGradient
      colors={['#1e1b4b', '#312e81', '#4338ca', '#6366f1']}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
      style={{ flex: 1 }}
    >
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />
      {/* Header with glass morphism */}
      <Box 
        className="flex-row justify-between items-center px-5 py-4 bg-white/10 backdrop-blur-md border-b border-white/20"
        style={[
          styles.glassHeader,
          { paddingTop: insets.top + 16 }
        ]}
      >
          <Text className="text-white text-lg font-bold">Scan Document</Text>
          
          <HStack className="gap-3">
            <Pressable onPress={toggleFlash}>
              <Box 
                className="p-2 rounded-full bg-white/10 backdrop-blur-md"
                style={[styles.glassButton, flash === 'on' && styles.activeFlash]}
              >
                <ZapIcon size={20} color="#FFFFFF" />
              </Box>
            </Pressable>
            <Pressable onPress={() => router.replace('../profile')}>
              <Box className="p-2 rounded-full bg-white/10 backdrop-blur-md" style={styles.glassButton}>
                <SettingsIcon size={20} color="#FFFFFF" />
              </Box>
            </Pressable>
          </HStack>
        </Box>

        {/* Tab Navigation with glass morphism */}
        <TabSelectorButtons
          activeTab={activeTab}
          onTabChange={handleTabChange}
          activeTabColor="#FF6B9D"
        />

        {/* Camera Viewfinder */}
        <CameraView
          ref={cameraRef}
          style={styles.camera}
          facing={facing}
          flash={flash}
        >
          <VStack className="flex-1 justify-center items-center px-5">
            <DocumentFrame isScanning={isCapturing} />
          </VStack>
        </CameraView>

        {/* Camera Controls */}
        <HStack className="justify-around items-center px-10 pb-10">
          <Pressable onPress={() => router.push('./gallery')}>
            <Box className="p-4 rounded-full bg-white/10 backdrop-blur-md" style={styles.glassButton}>
              <ImageIcon size={24} color="#FFFFFF" />
            </Box>
          </Pressable>
          
          <Pressable 
            onPress={takePicture}
            disabled={isCapturing}
            style={[styles.captureButton, isCapturing && styles.capturingButton]}
          >
            <Box className="w-18 h-18 rounded-full bg-white justify-center items-center">
              <CameraIcon size={28} color="#1e1b4b" />
            </Box>
          </Pressable>
          
          <Pressable onPress={toggleCameraFacing}>
            <Box className="p-4 rounded-full bg-white/10 backdrop-blur-md" style={styles.glassButton}>
              <RotateCcwIcon size={24} color="#FFFFFF" />
            </Box>
          </Pressable>
        </HStack>

        {/* Tips Section with glass morphism */}
        <Guideline
          state={guidelineState}
          bottomInset={insets.bottom}
          marginBottom={20}
          onReviewPress={handleReviewNavigation}
          onRetakePress={handleRetake}
          customTips={[
            'Ensure good lighting for clear scans',
            'Place document flat on surface', 
            'Align document with corner guides',
            'Auto-crop will detect edges'
          ]}
        />
    </LinearGradient>
  );
}

// StyleSheet following Rule #6 (prefer StyleSheet.create for static styles)
const styles = StyleSheet.create({
  glassFrame: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  glassHeader: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
  },
  glassButton: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  glassCard: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
  },
  activeFlash: {
    backgroundColor: '#FBBF24', // Yellow for active flash
  },
  captureButton: {
    width: 72,
    height: 72,
    borderRadius: 36,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  capturingButton: {
    opacity: 0.7,
    transform: [{ scale: 0.95 }],
  },
  // Corner indicator positions
  cornerTopLeft: {
    position: 'absolute',
    top: -3,
    left: -3,
  },
  cornerTopRight: {
    position: 'absolute',
    top: -3,
    right: -3,
  },
  cornerBottomLeft: {
    position: 'absolute',
    bottom: -3,
    left: -3,
  },
  cornerBottomRight: {
    position: 'absolute',
    bottom: -3,
    right: -3,
  },
  // Scanning line animation
  scanLine: {
    position: 'absolute',
    top: 20,
    left: 20,
    right: 20,
    height: 3,
    zIndex: 10,
  },
  scanLineGradient: {
    flex: 1,
    borderRadius: 2,
  },
  camera: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
});