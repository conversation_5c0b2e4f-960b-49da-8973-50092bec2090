// import { Tabs } from "expo-router";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>b<PERSON>rigger, TabSlot } from 'expo-router/ui';
import { Text } from '@/components/ui/text';
import { Box } from '@/components/ui/box';
import { Pressable, StyleSheet, Dimensions } from 'react-native';
import { useColorScheme } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import {
  HomeIcon,
  HeartIcon,
  MessageSquareIcon,
  SettingsIcon
} from 'lucide-react-native';
import Animated, { 
  useSharedValue, 
  useAnimatedStyle, 
  withSpring,
  interpolateColor,
  withTiming
} from 'react-native-reanimated';
import { forwardRef, useEffect } from 'react';
import { type TabTriggerSlotProps } from 'expo-router/ui';
import { useAuth } from '@/lib/contexts/AuthContext';
import AuthGuard from '@/components/auth/AuthGuard';

// Define candy colors using design tokens instead of hardcoded values
const candyColors = {
  green: '#10B981',   // success-500 from design tokens
  pink: '#FF6B9D',    // candy-pink from design tokens
  blue: '#3B82F6',    // tertiary-500 from design tokens  
  purple: '#8B5CF6',  // secondary-500 from design tokens
};

// Custom Floating Tab Button with animations and text labels
const FloatingTabButton = forwardRef<Animated.View, TabTriggerSlotProps & {
  icon: React.ReactNode;
  name: string;
  label: string;
  color: string;
}>((props, ref) => {
  const { icon, name, label, color, isFocused, ...otherProps } = props;
  
  // Animation values
  const scale = useSharedValue(1);
  const backgroundColor = useSharedValue(0);
  const textOpacity = useSharedValue(0);
  const containerWidth = useSharedValue(56);
  
  useEffect(() => {
    // Scale animation for active state
    scale.value = withSpring(isFocused ? 1.05 : 1, {
      damping: 15,
      stiffness: 300,
    });
    
    // Background color animation
    backgroundColor.value = withSpring(isFocused ? 1 : 0, {
      damping: 20,
      stiffness: 200,
    });
    
    // Text opacity and container width animation
    if (isFocused) {
      textOpacity.value = withTiming(1, { duration: 200 });
      containerWidth.value = withSpring(120, {
        damping: 20,
        stiffness: 200,
      });
    } else {
      textOpacity.value = withTiming(0, { duration: 150 });
      containerWidth.value = withSpring(56, {
        damping: 20,
        stiffness: 200,
      });
    }
  }, [isFocused]);
  
  const animatedStyle = useAnimatedStyle(() => {
    const bgColor = interpolateColor(
      backgroundColor.value,
      [0, 1],
      ['transparent', color]
    );
    
    return {
      transform: [{ scale: scale.value }],
      backgroundColor: bgColor,
      width: containerWidth.value,
    };
  });
  
  const textAnimatedStyle = useAnimatedStyle(() => {
    return {
      opacity: textOpacity.value,
    };
  });
  
  return (
    <Animated.View 
      ref={ref}
      style={animatedStyle}
      className="h-12 rounded-3xl justify-center items-center mx-1 flex-row px-3"
    >
      <Pressable
        {...otherProps}
        className="flex-row items-center justify-center flex-1 h-full"
      >
        <Box className={`${isFocused ? 'opacity-100' : 'opacity-60'}`}>
          {icon}
        </Box>
        {isFocused && (
          <Animated.View style={[textAnimatedStyle]} className="ml-2">
            <Text className="text-white text-sm font-semibold">
              {label}
            </Text>
          </Animated.View>
        )}
      </Pressable>
    </Animated.View>
  );
});

export const TabLayout = () => {
  const colorScheme = useColorScheme();
  const insets = useSafeAreaInsets();

  // Calculate minimal bottom spacing - just enough to prevent content overlap
  // Tab bar height (48px) + tiny buffer (8px) to prevent visual crowding
  const bottomSpacing = 48 + 8; // Minimal spacing for maximum content area

  return (
    <AuthGuard>
      <Tabs>
      {/* Content area with bottom padding to prevent overlap */}
      <Box className="flex-1">
        <TabSlot />
        {/* Dynamic bottom spacer to prevent content from being hidden behind floating tab bar */}
        <Box style={{ height: bottomSpacing }} />
      </Box>
      
      {/* Floating Tab Bar Container using Box and Tailwind */}
      <Box 
        className={`
          absolute left-5 right-5 
          rounded-[28px] py-3 px-4 
          flex-row justify-around items-center
          shadow-lg border
          ${colorScheme === 'dark' 
            ? 'bg-black/80 border-white/10' 
            : 'bg-white/90 border-black/5'
          }
        `}
        style={{ bottom: insets.bottom + 8 }} // Minimal margin for maximum screen utilization
      >
        <TabTrigger name="home" asChild>
          <FloatingTabButton 
            icon={<HomeIcon size={20} color={colorScheme === 'dark' ? '#FFFFFF' : '#1F2937'} />}
            name="home"
            label="Home"
            color={candyColors.green}
          />
        </TabTrigger>
        
        <TabTrigger name="scan" asChild>
          <FloatingTabButton 
            icon={<HeartIcon size={20} color={colorScheme === 'dark' ? '#FFFFFF' : '#1F2937'} />}
            name="scan"
            label="Scan"
            color={candyColors.pink}
          />
        </TabTrigger>
        
        <TabTrigger name="search" asChild>
          <FloatingTabButton 
            icon={<MessageSquareIcon size={20} color={colorScheme === 'dark' ? '#FFFFFF' : '#1F2937'} />}
            name="search"
            label="Chats"
            color={candyColors.blue}
          />
        </TabTrigger>
        
        <TabTrigger name="profile" asChild>
          <FloatingTabButton 
            icon={<SettingsIcon size={20} color={colorScheme === 'dark' ? '#FFFFFF' : '#1F2937'} />}
            name="profile"
            label="Profile"
            color={candyColors.purple}
          />
        </TabTrigger>
      </Box>
      
      {/* Hidden TabList - required by Expo Router */}
      <TabList style={{ display: 'none' }}>
        <TabTrigger name="home" href="/(tabs)/home">
          <Text>Home</Text>
        </TabTrigger>
        <TabTrigger name="scan" href="/(tabs)/scan">
          <Text>Scan</Text>
        </TabTrigger>
        <TabTrigger name="search" href="/(tabs)/search">
          <Text>Search</Text>
        </TabTrigger>
        <TabTrigger name="profile" href="/(tabs)/profile">
          <Text>Profile</Text>
        </TabTrigger>
      </TabList>
    </Tabs>
    </AuthGuard>
  );
}

export default TabLayout;
