import React, { useState, useEffect } from 'react';
import { ScrollView, Image, StatusBar, Alert } from 'react-native';
import { Text } from '../../components/ui/text';
import { VStack } from '../../components/ui/vstack';
import { HStack } from '../../components/ui/hstack';
import { Box } from '../../components/ui/box';
import { Button, ButtonText } from '../../components/ui/button';
import { Badge, BadgeText } from '../../components/ui/badge';
import { Switch } from '../../components/ui/switch';
import { Progress, ProgressFilledTrack } from '../../components/ui/progress';
import { Select, SelectTrigger, SelectInput, SelectIcon, SelectPortal, SelectBackdrop, SelectContent, SelectDragIndicatorWrapper, SelectDragIndicator, SelectItem } from '../../components/ui/select';
import { useRouter } from 'expo-router';
import { useAuth } from '@/lib/contexts/AuthContext';
import {
  UserIcon,
  SettingsIcon,
  StarIcon,
  CloudIcon,
  BellIcon,
  MoonIcon,
  ShieldIcon,
  HelpCircleIcon,
  LogOutIcon,
  DownloadIcon
} from 'lucide-react-native';

export default function Profile() {
  const router = useRouter();
  const { user: authUser, userProfile, isAuthenticated, isAnonymous, signOut, updateUserProfile } = useAuth();

  const [localSettings, setLocalSettings] = useState({
    autoEnhance: true,
    cloudSync: true,
    notifications: true,
    darkMode: false,
    language: 'en',
    quality: 'high' as const,
    autoBackup: true,
    shareAnalytics: false
  });

  // Sync settings with user profile when available
  useEffect(() => {
    if (userProfile?.preferences) {
      setLocalSettings(prev => ({
        ...prev,
        notifications: userProfile.preferences.notifications,
        darkMode: userProfile.preferences.theme === 'dark',
        language: userProfile.preferences.language,
      }));
    }
  }, [userProfile]);

  const [settings, setSettings] = useState({
    autoEnhance: true,
    cloudSync: true,
    notifications: true,
    darkMode: false,
    language: 'en',
    quality: 'high' as const,
    autoBackup: true,
    shareAnalytics: false
  });

  const handleSettingChange = (key: string, value: any) => {
    setSettings(prev => ({ ...prev, [key]: value }));
  };

  const handleUpgrade = () => {
    console.log('Navigate to upgrade screen');
  };

  const handleSignOut = () => {
    console.log('Sign out user');
  };

  return (
    <ScrollView className="flex-1 bg-background-0">
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />
      <VStack space="lg" className="p-6 pb-24">
        {/* Profile Header */}
        <Box className="bg-glass-bg-primary border border-glass-border-primary backdrop-blur-md rounded-xl p-6">
          <VStack space="md">
            <HStack className="items-center space-x-4">
              <Box className="w-16 h-16 rounded-full overflow-hidden bg-glass-bg-secondary">
                <Image 
                  source={{ uri: user.avatar }}
                  className="w-full h-full"
                  resizeMode="cover"
                />
              </Box>
              
              <VStack space="xs" className="flex-1">
                <Text color="primary" size="xl" style={{ fontWeight: 'bold' }}>
                  {user.name}
                </Text>
                <Text color="secondary" size="md">
                  {user.email}
                </Text>
                <HStack className="items-center space-x-2">
                  <Badge action="candyPink" variant="solid" size="sm">
                    <BadgeText>{user.plan}</BadgeText>
                  </Badge>
                  {user.plan !== 'Premium' && (
                    <Button action="candyPink" variant="outline" size="xs" onPress={handleUpgrade}>
                      <ButtonText>Upgrade</ButtonText>
                    </Button>
                  )}
                </HStack>
              </VStack>
            </HStack>
          </VStack>
        </Box>

        {/* Usage Statistics */}
        <VStack space="md">
          <Text color="primary" size="lg" style={{ fontWeight: 'bold' }}>
            Usage Statistics
          </Text>
          
          <HStack className="space-x-3">
            <Box className="flex-1 bg-glass-bg-card border border-glass-border-secondary backdrop-blur-md rounded-xl p-4">
              <VStack space="xs">
                <Text color="candyPink" size="2xl" style={{ fontWeight: 'bold' }}>
                  {user.documentsScanned}
                </Text>
                <Text color="secondary" size="sm">
                  Documents Scanned
                </Text>
              </VStack>
            </Box>
            
            <Box className="flex-1 bg-glass-bg-card border border-glass-border-secondary backdrop-blur-md rounded-xl p-4">
              <VStack space="xs">
                <Text color="candyPurple" size="2xl" style={{ fontWeight: 'bold' }}>
                  {user.storageUsed}GB
                </Text>
                <Text color="secondary" size="sm">
                  Storage Used
                </Text>
                <Progress value={(user.storageUsed / user.storageLimit) * 100} size="sm">
                  <ProgressFilledTrack />
                </Progress>
              </VStack>
            </Box>
          </HStack>
        </VStack>

        {/* Scan Settings */}
        <VStack space="md">
          <Text color="primary" size="lg" style={{ fontWeight: 'bold' }}>
            Scan Settings
          </Text>
          
          <VStack space="sm">
            <Box className="bg-glass-bg-card border border-glass-border-secondary backdrop-blur-md rounded-xl p-4">
              <HStack className="justify-between items-center">
                <VStack space="xs" className="flex-1">
                  <Text color="primary" size="md" style={{ fontWeight: '600' }}>
                    Auto Enhancement
                  </Text>
                  <Text color="secondary" size="sm">
                    Automatically enhance scanned images
                  </Text>
                </VStack>
                <Switch
                  value={settings.autoEnhance}
                  onValueChange={(value) => handleSettingChange('autoEnhance', value)}
                />
              </HStack>
            </Box>

            <Box className="bg-glass-bg-card border border-glass-border-secondary backdrop-blur-md rounded-xl p-4">
              <HStack className="justify-between items-center">
                <VStack space="xs" className="flex-1">
                  <Text color="primary" size="md" style={{ fontWeight: '600' }}>
                    Scan Quality
                  </Text>
                  <Text color="secondary" size="sm">
                    Default quality for new scans
                  </Text>
                </VStack>
                <Select
                  selectedValue={settings.quality}
                  onValueChange={(value) => handleSettingChange('quality', value)}
                >
                  <SelectTrigger variant="outline" size="sm">
                    <SelectInput placeholder="Select quality" />
                    <SelectIcon />
                  </SelectTrigger>
                  <SelectPortal>
                    <SelectBackdrop />
                    <SelectContent>
                      <SelectDragIndicatorWrapper>
                        <SelectDragIndicator />
                      </SelectDragIndicatorWrapper>
                      <SelectItem label="High" value="high" />
                      <SelectItem label="Medium" value="medium" />
                      <SelectItem label="Low" value="low" />
                    </SelectContent>
                  </SelectPortal>
                </Select>
              </HStack>
            </Box>

            <Box className="bg-glass-bg-card border border-glass-border-secondary backdrop-blur-md rounded-xl p-4">
              <HStack className="justify-between items-center">
                <VStack space="xs" className="flex-1">
                  <Text color="primary" size="md" style={{ fontWeight: '600' }}>
                    Auto Backup
                  </Text>
                  <Text color="secondary" size="sm">
                    Automatically backup to cloud
                  </Text>
                </VStack>
                <Switch
                  value={settings.autoBackup}
                  onValueChange={(value) => handleSettingChange('autoBackup', value)}
                />
              </HStack>
            </Box>
          </VStack>
        </VStack>

        {/* App Settings */}
        <VStack space="md">
          <Text color="primary" size="lg" style={{ fontWeight: 'bold' }}>
            App Settings
          </Text>
          
          <VStack space="sm">
            <Box className="bg-glass-bg-card border border-glass-border-secondary backdrop-blur-md rounded-xl p-4">
              <HStack className="justify-between items-center">
                <VStack space="xs" className="flex-1">
                  <Text color="primary" size="md" style={{ fontWeight: '600' }}>
                    Cloud Sync
                  </Text>
                  <Text color="secondary" size="sm">
                    Sync documents across devices
                  </Text>
                </VStack>
                <Switch
                  value={settings.cloudSync}
                  onValueChange={(value) => handleSettingChange('cloudSync', value)}
                />
              </HStack>
            </Box>

            <Box className="bg-glass-bg-card border border-glass-border-secondary backdrop-blur-md rounded-xl p-4">
              <HStack className="justify-between items-center">
                <VStack space="xs" className="flex-1">
                  <Text color="primary" size="md" style={{ fontWeight: '600' }}>
                    Notifications
                  </Text>
                  <Text color="secondary" size="sm">
                    Receive scan completion alerts
                  </Text>
                </VStack>
                <Switch
                  value={settings.notifications}
                  onValueChange={(value) => handleSettingChange('notifications', value)}
                />
              </HStack>
            </Box>

            <Box className="bg-glass-bg-card border border-glass-border-secondary backdrop-blur-md rounded-xl p-4">
              <HStack className="justify-between items-center">
                <VStack space="xs" className="flex-1">
                  <Text color="primary" size="md" style={{ fontWeight: '600' }}>
                    Dark Mode
                  </Text>
                  <Text color="secondary" size="sm">
                    Use dark theme
                  </Text>
                </VStack>
                <Switch
                  value={settings.darkMode}
                  onValueChange={(value) => handleSettingChange('darkMode', value)}
                />
              </HStack>
            </Box>
          </VStack>
        </VStack>

        {/* Premium Features */}
        {user.plan !== 'Premium' && (
          <Box className="bg-gradient-to-r from-candyPink/10 to-candyPurple/10 border border-candyPink/20 rounded-xl p-6">
            <VStack space="md">
              <HStack className="items-center space-x-3">
                <Box className="w-10 h-10 justify-center items-center rounded-full bg-candyPink/20">
                  <StarIcon size={20} color="#FF6B9D" />
                </Box>
                <Text color="primary" size="lg" style={{ fontWeight: 'bold' }}>
                  Upgrade to Premium
                </Text>
              </HStack>
              
              <VStack space="sm">
                <Text color="primary" size="sm">
                  • Unlimited document scanning
                </Text>
                <Text color="primary" size="sm">
                  • Advanced AI features
                </Text>
                <Text color="primary" size="sm">
                  • Priority cloud sync
                </Text>
                <Text color="primary" size="sm">
                  • Export to multiple formats
                </Text>
              </VStack>
              
              <Button action="candyPink" size="md" onPress={handleUpgrade}>
                <ButtonText>Upgrade Now</ButtonText>
              </Button>
            </VStack>
          </Box>
        )}

        {/* Account Actions */}
        <VStack space="md">
          <Text color="primary" size="lg" style={{ fontWeight: 'bold' }}>
            Account
          </Text>
          
          <VStack space="sm">
            <Button action="glass" variant="outline" size="md">
              <ButtonText>Export Data</ButtonText>
            </Button>
            
            <Button action="glass" variant="outline" size="md">
              <ButtonText>Privacy Settings</ButtonText>
            </Button>
            
            <Button action="glass" variant="outline" size="md">
              <ButtonText>Help & Support</ButtonText>
            </Button>
            
            <Button action="negative" variant="outline" size="md" onPress={handleSignOut}>
              <ButtonText>Sign Out</ButtonText>
            </Button>
          </VStack>
        </VStack>
      </VStack>
    </ScrollView>
  );
}
