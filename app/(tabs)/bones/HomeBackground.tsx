import React from 'react';
import { Dimensions, StyleSheet } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { LinearGradient } from 'expo-linear-gradient';
import { Box } from '@/components/ui/box';
import { FloatingShape } from '@/components/learni-scan/FloatingShape';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

export const HomeBackground: React.FC = () => {
  const { top: topInset } = useSafeAreaInsets();

  return (
    <>
    <StatusBar 
      style="light" 
      translucent
    />
    <Box className="absolute inset-0">
      {/* Background Gradient */}
      <LinearGradient
        colors={[
          'rgb(255, 107, 157)', // candy-pink - more vibrant
          'rgb(168, 85, 247)',  // candy-purple
          'rgb(59, 130, 246)'   // candy-blue
        ]}
        locations={[0, 0.6, 1]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.gradient}
      />


      {/* Floating Background Shapes */}
      <FloatingShape
        size={80}
        top={screenHeight * 0.2}
        left={screenWidth * 0.1}
        animationDuration={6000}
      />
      <FloatingShape
        size={120}
        top={screenHeight * 0.6}
        right={screenWidth * 0.15}
        animationDuration={8000}
        delay={1000}
      />
      <FloatingShape
        size={60}
        bottom={screenHeight * 0.3}
        left={screenWidth * 0.2}
        animationDuration={7000}
        delay={2000}
      />
      <FloatingShape
        size={100}
        top={screenHeight * 0.1}
        right={screenWidth * 0.3}
        animationDuration={9000}
        delay={3000}
      />
    </Box>
    </>
  );
};


const styles = StyleSheet.create({
  gradient: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
});

export default HomeBackground;
