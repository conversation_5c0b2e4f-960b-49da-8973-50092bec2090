import React, { useState } from 'react';
import { 
  StyleSheet,
  LayoutAnimation, 
  UIManager, 
  Platform 
} from 'react-native';
import { 
  MinimizeIcon,
  ExpandIcon,
  TextSelectIcon
} from 'lucide-react-native';
import { Button, ButtonGroup, ButtonText, ButtonIcon } from '@/components/ui/button';
import { Pressable } from '@/components/ui/pressable';
import { Box } from '@/components/ui/box';
import { Text } from '@/components/ui/text';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';

// Enable LayoutAnimation on Android
if (Platform.OS === 'android') {
  if (UIManager.setLayoutAnimationEnabledExperimental) {
    UIManager.setLayoutAnimationEnabledExperimental(true);
  }
}

interface IGuidelineProps {
  /** Current state: 'scanning' | 'captured' | 'processing' */
  state: 'scanning' | 'captured' | 'processing';
  /** Bottom safe area inset for proper spacing */
  bottomInset?: number;
  /** Custom margin from bottom */
  marginBottom?: number;
  /** Callback when user wants to navigate to review */
  onReviewPress?: () => void;
  /** Callback when user wants to retake/reset */
  onRetakePress?: () => void;
  /** Additional custom tips for scanning state */
  customTips?: string[];
  /** Show/hide the component initially */
  initialExpanded?: boolean;
}

export const Guideline: React.FC<IGuidelineProps> = ({
  state,
  bottomInset = 0,
  marginBottom = 20,
  onReviewPress,
  onRetakePress,
  customTips,
  initialExpanded = true,
}) => {
  const [expanded, setExpanded] = useState(initialExpanded);

  // Animation configurations
  const toggleExpanded = () => {
    LayoutAnimation.configureNext({
      duration: 300,
      create: {
        type: LayoutAnimation.Types.easeInEaseOut,
        property: LayoutAnimation.Properties.opacity,
      },
      update: {
        type: LayoutAnimation.Types.easeInEaseOut,
        property: LayoutAnimation.Properties.scaleXY,
      },
    });
    setExpanded(!expanded);
  };

  const handleReviewPress = () => {
    LayoutAnimation.configureNext({
      duration: 200,
      update: {
        type: LayoutAnimation.Types.easeOut,
        property: LayoutAnimation.Properties.opacity,
      },
    });
    onReviewPress?.();
  };

  const handleRetakePress = () => {
    LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
    onRetakePress?.();
  };

  // Get content based on current state
  const getContent = () => {
    switch (state) {
      case 'captured':
        return {
          icon: '🎯',
          title: 'Ready to Process',
          content: (
            <VStack className="gap-2">
              <HStack className="items-center gap-2 p-3 rounded-lg bg-green-500/20 border border-green-400/30">
                <Text className="text-green-400 text-sm font-bold">✓ Photo captured successfully!</Text>
              </HStack>
              <VStack className="gap-1 mt-2">
                <Text className="text-white text-sm font-semibold">Next steps:</Text>
                <Text className="text-white/80 text-sm">• Tap "Process Text" to extract text</Text>
                <Text className="text-white/80 text-sm">• Edit and translate content</Text>
                <Text className="text-white/80 text-sm">• Save or share your results</Text>
              </VStack>
              
              {/* Quick action buttons */}
              <HStack className="gap-3 mt-3">
                <Pressable
                  onPress={handleReviewPress}
                  className="rounded-xl"
                  variant="glass"
                  action="candyPurple"
                >
                  <HStack className="items-center justify-center gap-2">
                    <Text className="text-white text-sm font-bold">Process Text</Text>
                    <Text className="text-white">→</Text>
                  </HStack>
                </Pressable>
                
                {onRetakePress && (
                  <Pressable
                    onPress={handleRetakePress}
                    className="rounded-xl bg-white/20 border border-white/30"
                    variant="glass"
                    action="candyPurple"
                  >
                    <Text className="text-white text-sm font-medium">Retake</Text>
                  </Pressable>
                )}
              </HStack>
            </VStack>
          ),
        };

      case 'processing':
        return {
          icon: '⚡',
          title: 'Processing...',
          content: (
            <VStack className="gap-2">
              <HStack className="items-center gap-2 p-3 rounded-lg bg-blue-500/20 border border-blue-400/30">
                <Text className="text-blue-400 text-sm font-bold">🔄 Extracting text from image...</Text>
              </HStack>
              <VStack className="gap-1 mt-2">
                <Text className="text-white/80 text-sm">• Analyzing document structure</Text>
                <Text className="text-white/80 text-sm">• Recognizing text content</Text>
                <Text className="text-white/80 text-sm">• Preparing results</Text>
              </VStack>
            </VStack>
          ),
        };

      default: // 'scanning'
        const defaultTips = [
          'Ensure good lighting for clear scans',
          'Place document flat on surface',
          'Align document with corner guides',
          'Tap capture when ready to process',
        ];
        
        const tips = customTips || defaultTips;

        return {
          icon: '💡',
          title: 'Scanning Tips',
          content: (
            <VStack className="gap-1">
              {tips.map((tip, index) => (
                <Text key={index} className="text-white text-sm">• {tip}</Text>
              ))}
            </VStack>
          ),
        };
    }
  };

  const { icon, title, content } = getContent();

  return (
    <Box 
      className="mx-5 bg-white/10 rounded-2xl backdrop-blur-md shadow-lg"
      style={{ marginBottom: bottomInset + marginBottom }}
    >
      {/* Header with button group */}
      <HStack className="justify-between items-center p-4 pb-2">
        <HStack className="items-center">
          <Text className="text-yellow-400 font-bold">
            {icon} {title}
          </Text>
        </HStack>
        
        {/* Button group */}
        <HStack className="gap-2">
          <ButtonGroup space="sm" flexDirection="row">
            {!expanded ? (
              <Button className="h-8 rounded-full bg-white/20" onPress={handleReviewPress}>
                <ButtonText>Process</ButtonText>
                <ButtonIcon as={TextSelectIcon} />
              </Button>
            ) : <React.Fragment />}
            <Button className="w-8 h-8 rounded-full bg-white/20" onPress={toggleExpanded}>
              <ButtonIcon as={ expanded ? MinimizeIcon : ExpandIcon} />
            </Button>
          </ButtonGroup>
        </HStack>
      </HStack>

      {/* Animated content */}
      {expanded && (
        <Box className="px-4 pb-4">
          {content}
        </Box>
      )}
    </Box>
  );
};

const styles = StyleSheet.create({
  primaryActionGradient: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  reviewButtonGradient: {
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  controlButton: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
});
