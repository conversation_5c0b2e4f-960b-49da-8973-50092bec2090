import React from 'react';
import Animated, { 
  useSharedValue, 
  useAnimatedStyle, 
  withRepeat, 
  withTiming,
  Easing
} from 'react-native-reanimated';
import { VStack } from '@/components/ui/vstack';
import { Box } from '@/components/ui/box';
import { Text } from '@/components/ui/text';
import { FeatureCard } from './FeatureCard';
import { CameraIcon, LightbulbIcon, ShareIcon } from 'lucide-react-native';
import { useRouter } from 'expo-router';
import { CTAButtons } from './CTAButtons';

export const FeatureGrid: React.FC = () => {
  const router = useRouter();

  // Floating animation for feature cards (up and down)
  const translateY = useSharedValue(0);

  React.useEffect(() => {
    translateY.value = withRepeat(
      withTiming(-8, {
        duration: 2000,
        easing: Easing.inOut(Easing.ease),
      }),
      -1,
      true
    );
  }, []);

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateY: translateY.value }],
    };
  });

  const features = [
    {
      title: "Smart Scanning",
      description: "Instantly digitize any document with advanced OCR technology",
      icon: CameraIcon,
      onPress: () => router.push('/(tabs)/scan'),
      cta: <CTAButtons />
    },
    {
      title: "AI Learning",
      description: "Generate knowledge cards and personalized study paths",
      icon: LightbulbIcon,
      onPress: () => {}
    },
    {
      title: "Social Learning",
      description: "Share knowledge and collaborate with others",
      icon: ShareIcon,
      onPress: () => {}
    }
  ];

  return (
    <Box className="w-full mb-12">
      <VStack space="lg" className="items-center w-full">
        <Text 
          size="3xl" 
          className="text-white text-center font-bold mb-8 opacity-95"
        >
          Powerful Features
        </Text>
        <VStack space="md" className="px-4">
          {features.map((feature, index) => (
            <FeatureCard
              key={index}
              className="h-[190px]"
              title={feature.title}
              description={feature.description}
              icon={feature.icon}
              onPress={feature.onPress}
            >
              {feature.cta}
            </FeatureCard>
          ))}
        </VStack>
      </VStack>
    </Box>
  );
};