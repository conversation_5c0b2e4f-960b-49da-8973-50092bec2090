import React from 'react';
import { <PERSON><PERSON><PERSON> } from './HeroLogo';
import { HeroHeading } from './HeroHeading';
import { FeatureGrid } from './FeatureGrid';
import { CTAButtons } from './CTAButtons';
import { Box } from '@/components/ui/box';

interface HeroSectionProps {
  onGetStarted?: () => void;
  onWatchDemo?: () => void;
}

export const HeroSection: React.FC<HeroSectionProps> = ({
  onGetStarted,
  onWatchDemo
}) => {
  return (
    <Box className="flex-1 items-center justify-center px-6 py-12">
      <Box className="w-full items-center">
        {/* Logo with brightness animation */}
        <HeroLogo />
        
        {/* Heading with floating animation */}
        <HeroHeading appName="LearniScan" />
        
        {/* Feature grid with floating animation */}
        <FeatureGrid />
        
        {/* CTA buttons (no animation for now) */}
        <CTAButtons
          onGetStarted={onGetStarted}
          onWatchDemo={onWatchDemo}
        />
      </Box>
    </Box>
  );
};

export default HeroSection;