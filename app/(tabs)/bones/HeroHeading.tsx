import React from 'react';
import { VStack } from '@/components/ui/vstack';
import { Text } from '@/components/ui/text';
import { Dimensions, StyleSheet } from 'react-native';
import Animated, { 
  useSharedValue, 
  useAnimatedStyle, 
  withRepeat, 
  withTiming,
  Easing
} from 'react-native-reanimated';
import GradientText from '@/components/learni-scan/GradientText';

const { width: screenWidth } = Dimensions.get('window');

interface HeroHeadingProps {
  welcomeText?: string;
  appName?: string;
  tagline?: string;
  description?: string;
}

const gradientColors = ['#FF6B9D', '#C44CEA', '#4F46E5', '#06B6D4'] as const;

export const HeroHeading: React.FC<HeroHeadingProps> = ({
  welcomeText = "Welcome to",
  appName = "LearniScan",
  tagline = "Scan. Learn. Share.",
  description = "Transform any document into an interactive learning experience with AI-powered knowledge cards, translations, and personalized study roadmaps.",
}) => {
  // Floating animation for text (up and down)
  const translateY = useSharedValue(0);

  React.useEffect(() => {
    translateY.value = withRepeat(
      withTiming(-8, {
        duration: 2000,
        easing: Easing.inOut(Easing.ease),
      }),
      -1,
      true
    );
  }, []);

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateY: translateY.value }],
    };
  });

  return (
    <>
      {/* Main Heading */}
      <Animated.View style={animatedStyle}>
        <VStack space="lg" className="items-center mb-8">
          <Text
            size="4xl"
            className="text-white text-center font-bold opacity-90"
            style={styles.welcomeText}
          >
            {welcomeText}
          </Text>
          <GradientText
            colors={gradientColors}
            fontSize={screenWidth >= 768 ? 80 : 64}
            fontWeight="bold"
            containerStyle={styles.gradientContainer}
          >
            {appName}
          </GradientText>
        </VStack>
      </Animated.View>

      {/* Tagline & Description */}
      <VStack space="lg" className="items-center mb-16">
        <Text
          size="2xl"
          className="text-white text-center font-semibold opacity-95"
          style={styles.taglineText}
        >
          {tagline}
        </Text>
        <Text
          size="lg"
          className="text-white text-center leading-relaxed px-8 opacity-85"
          style={styles.descriptionText}
        >
          {description}
        </Text>
      </VStack>
    </>
  );
};

const styles = StyleSheet.create({
  welcomeText: {
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
    letterSpacing: 0.5,
  },
  gradientContainer: {
    marginVertical: 8,
  },
  taglineText: {
    textShadowColor: 'rgba(0, 0, 0, 0.2)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 3,
    letterSpacing: 0.3,
  },
  descriptionText: {
    textShadowColor: 'rgba(0, 0, 0, 0.2)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
    lineHeight: 28,
  },
});