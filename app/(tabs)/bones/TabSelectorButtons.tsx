import React from 'react';
import { Pressable } from '@/components/ui/pressable';
import { Box } from '@/components/ui/box';
import { Text } from '@/components/ui/text';

export type TabType = 'camera' | 'upload' | 'gallery';

interface ITabSelectorButtonsProps {
  /** Currently active tab */
  activeTab: TabType;
  /** Callback when tab is changed */
  onTabChange: (tab: TabType) => void;
  /** Custom active tab background color - defaults to candy pink */
  activeTabColor?: string;
  /** Custom container class name */
  containerClassName?: string;
}

export const TabSelectorButtons: React.FC<ITabSelectorButtonsProps> = ({
  activeTab,
  onTabChange,
  activeTabColor = '#FF6B9D', // Default candy pink from Camera
  containerClassName = "px-5 pt-5"
}) => {
  const tabs: TabType[] = ['camera', 'upload', 'gallery'];

  return (
    <Box className={containerClassName}>
      <Box 
        className="flex-row bg-white/10 p-1 rounded-2xl backdrop-blur-md min-h-16 shadow-md"
      >
        {tabs.map((tab) => (
          <Pressable
            key={tab}
            onPress={() => onTabChange(tab)}
            className="flex-1 py-5 px-4 rounded-xl items-center justify-center min-h-14"
            style={[
              activeTab === tab && { backgroundColor: activeTabColor }
            ]}
          >
            <Text 
              className={`capitalize font-medium text-sm leading-4 text-center ${
                activeTab === tab ? 'text-white font-bold' : 'text-white/70'
              }`}
            >
              {tab}
            </Text>
          </Pressable>
        ))}
      </Box>
    </Box>
  );
};
