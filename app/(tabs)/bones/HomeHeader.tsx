import React from 'react';
import { HStack } from '@/components/ui/hstack';
import { Box } from '@/components/ui/box';
import { Text } from '@/components/ui/text';
import { Pressable } from '@/components/ui/pressable';
import { Brain } from 'lucide-react-native';
import { Link } from 'expo-router';

interface HomeHeaderProps {
  onSkipTour?: () => void;
}

export const HomeHeader: React.FC<HomeHeaderProps> = ({ onSkipTour }) => {
  return (
    <Box className="px-6 pt-4 pb-2">
      <HStack className="justify-between items-center w-full">
        <Pressable
          onPress={onSkipTour}
          variant="glass"
          action="candyPink"
          size="lg"
        >
          <Text color="primary" size="lg" className="font-semibold">
            LearniScan
          </Text>
        </Pressable>

        <HStack className="space-x-3">
          {/* Knowledge Module Entry */}
          <Link href="/(knowledge)/graph" asChild>
            <Pressable
              variant="glass"
              action="candyPurple"
              size="md"
              className="flex-row items-center space-x-2 px-3 py-2"
            >
              <Brain size={18} color="white" />
              <Text color="primary" size="sm" className="font-medium text-white">
                Knowledge
              </Text>
            </Pressable>
          </Link>

          <Pressable
            onPress={onSkipTour}
            variant="glass"
            action="candyPink"
            size="lg"
          >
            <Text color="primary" size="lg" className="font-semibold">
              Skip Tour
            </Text>
          </Pressable>
        </HStack>
      </HStack>
    </Box>
  );
};

export default HomeHeader;
