import React, { useState, useRef } from 'react';
import { ScrollView } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { WebView } from 'react-native-webview';
import { ArrowLeft, Search, Settings, Plus, Minus, X, RotateCcw, Maximize2 } from 'lucide-react-native';

// UI Components
import { Text } from '@/components/ui/text';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Box } from '@/components/ui/box';
import { Button, ButtonText } from '@/components/ui/button';
import { Pressable } from '@/components/ui/pressable';
import { LinearGradient } from 'expo-linear-gradient';

export const Graph: React.FC = () => {
  const webViewRef = useRef<WebView>(null);
  const [selectedNode, setSelectedNode] = useState<string | null>(null);
  const [zoomLevel, setZoomLevel] = useState(1);
  const [viewMode, setViewMode] = useState<'overview' | 'detailed' | 'network'>('overview');

  // Mermaid diagram definition
  const getMermaidDiagram = () => {
    switch (viewMode) {
      case 'overview':
        return `
          flowchart TD
            ML[Machine Learning] --> SL[Supervised Learning]
            ML --> UL[Unsupervised Learning]
            ML --> RL[Reinforcement Learning]

            SL --> C[Classification]
            SL --> R[Regression]

            UL --> CL[Clustering]
            UL --> DR[Dimensionality Reduction]

            RL --> QL[Q-Learning]
            RL --> PG[Policy Gradient]

            C --> LR[Logistic Regression]
            C --> SVM[Support Vector Machine]
            C --> RF[Random Forest]

            R --> LIN[Linear Regression]
            R --> POLY[Polynomial Regression]

            CL --> KM[K-Means]
            CL --> HC[Hierarchical Clustering]

            DR --> PCA[Principal Component Analysis]
            DR --> LDA[Linear Discriminant Analysis]

            style ML fill:#FF6B9D,stroke:#fff,stroke-width:3px,color:#fff
            style SL fill:#A855F7,stroke:#fff,stroke-width:2px,color:#fff
            style UL fill:#3B82F6,stroke:#fff,stroke-width:2px,color:#fff
            style RL fill:#06B6D4,stroke:#fff,stroke-width:2px,color:#fff
        `;
      case 'detailed':
        return `
          mindmap
            root((Machine Learning))
              Supervised Learning
                Classification
                  Logistic Regression
                  SVM
                  Random Forest
                  Neural Networks
                Regression
                  Linear Regression
                  Polynomial Regression
                  Ridge Regression
              Unsupervised Learning
                Clustering
                  K-Means
                  Hierarchical
                  DBSCAN
                Dimensionality Reduction
                  PCA
                  t-SNE
                  LDA
              Reinforcement Learning
                Q-Learning
                Policy Gradient
                Actor-Critic
        `;
      case 'network':
        return `
          graph TB
            subgraph "Core ML Concepts"
              ML[Machine Learning]
              DATA[Data Processing]
              EVAL[Model Evaluation]
            end

            subgraph "Supervised Learning"
              SL[Supervised Learning]
              CLASS[Classification]
              REG[Regression]
              LR[Logistic Regression]
              SVM[Support Vector Machine]
              RF[Random Forest]
              LIN[Linear Regression]
            end

            subgraph "Unsupervised Learning"
              UL[Unsupervised Learning]
              CLUST[Clustering]
              DR[Dimensionality Reduction]
              KM[K-Means]
              HC[Hierarchical Clustering]
              PCA[Principal Component Analysis]
            end

            subgraph "Reinforcement Learning"
              RL[Reinforcement Learning]
              QL[Q-Learning]
              PG[Policy Gradient]
              AC[Actor-Critic]
            end

            ML --> SL
            ML --> UL
            ML --> RL
            ML --> DATA
            ML --> EVAL

            SL --> CLASS
            SL --> REG
            CLASS --> LR
            CLASS --> SVM
            CLASS --> RF
            REG --> LIN

            UL --> CLUST
            UL --> DR
            CLUST --> KM
            CLUST --> HC
            DR --> PCA

            RL --> QL
            RL --> PG
            RL --> AC

            DATA --> SL
            DATA --> UL
            EVAL --> SL
            EVAL --> UL
            EVAL --> RL

            style ML fill:#FF6B9D,stroke:#fff,stroke-width:3px,color:#fff
            style SL fill:#A855F7,stroke:#fff,stroke-width:2px,color:#fff
            style UL fill:#3B82F6,stroke:#fff,stroke-width:2px,color:#fff
            style RL fill:#06B6D4,stroke:#fff,stroke-width:2px,color:#fff
            style DATA fill:#10B981,stroke:#fff,stroke-width:2px,color:#fff
            style EVAL fill:#F59E0B,stroke:#fff,stroke-width:2px,color:#fff
        `;
      default:
        return `
          graph LR
            A[Machine Learning] --> B[Supervised]
            A --> C[Unsupervised]
            A --> D[Reinforcement]
        `;
    }
  };

  // HTML template for Mermaid rendering
  const getMermaidHTML = () => `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1">
      <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
      <style>
        body {
          margin: 0;
          padding: 20px;
          background: transparent;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          overflow: hidden;
        }

        #mermaid-container {
          width: 100%;
          height: 100vh;
          display: flex;
          justify-content: center;
          align-items: center;
        }

        .mermaid {
          max-width: 100%;
          max-height: 100%;
        }

        /* Custom Mermaid styling for LearniScan theme */
        .node rect, .node circle, .node ellipse, .node polygon {
          fill: rgba(255, 107, 157, 0.8) !important;
          stroke: rgba(255, 255, 255, 0.6) !important;
          stroke-width: 2px !important;
        }

        .node text {
          fill: white !important;
          font-weight: 600 !important;
        }

        .edgePath path {
          stroke: rgba(255, 255, 255, 0.4) !important;
          stroke-width: 2px !important;
        }

        .arrowheadPath {
          fill: rgba(255, 255, 255, 0.4) !important;
        }

        /* Hover effects */
        .node:hover rect, .node:hover circle, .node:hover ellipse, .node:hover polygon {
          fill: rgba(168, 85, 247, 0.9) !important;
          stroke: rgba(255, 255, 255, 0.8) !important;
          cursor: pointer;
        }
      </style>
    </head>
    <body>
      <div id="mermaid-container">
        <div class="mermaid">
          ${getMermaidDiagram()}
        </div>
      </div>

      <script>
        mermaid.initialize({
          startOnLoad: true,
          theme: 'dark',
          themeVariables: {
            primaryColor: '#FF6B9D',
            primaryTextColor: '#ffffff',
            primaryBorderColor: '#ffffff',
            lineColor: '#ffffff',
            secondaryColor: '#A855F7',
            tertiaryColor: '#3B82F6',
            background: 'transparent',
            mainBkg: 'rgba(255, 107, 157, 0.8)',
            secondBkg: 'rgba(168, 85, 247, 0.8)',
            tertiaryBkg: 'rgba(59, 130, 246, 0.8)'
          },
          flowchart: {
            useMaxWidth: true,
            htmlLabels: true,
            curve: 'basis'
          },
          mindmap: {
            useMaxWidth: true,
            padding: 10
          }
        });

        // Handle node clicks
        document.addEventListener('click', function(event) {
          const target = event.target.closest('.node');
          if (target) {
            const nodeText = target.querySelector('text')?.textContent || '';
            window.ReactNativeWebView?.postMessage(JSON.stringify({
              type: 'nodeClick',
              nodeId: nodeText,
              position: { x: event.clientX, y: event.clientY }
            }));
          }
        });

        // Handle zoom events
        let currentZoom = 1;
        window.addEventListener('message', function(event) {
          const data = JSON.parse(event.data);
          if (data.type === 'zoom') {
            currentZoom = data.level;
            const container = document.getElementById('mermaid-container');
            container.style.transform = \`scale(\${currentZoom})\`;
          }
        });

        // Notify React Native that the diagram is ready
        setTimeout(() => {
          window.ReactNativeWebView?.postMessage(JSON.stringify({
            type: 'ready',
            zoom: currentZoom
          }));
        }, 1000);
      </script>
    </body>
    </html>
  `;

  // Handle messages from WebView
  const handleWebViewMessage = (event: any) => {
    try {
      const data = JSON.parse(event.nativeEvent.data);

      switch (data.type) {
        case 'nodeClick':
          setSelectedNode(data.nodeId);
          break;
        case 'ready':
          // Diagram is ready, apply initial zoom
          if (webViewRef.current) {
            webViewRef.current.postMessage(JSON.stringify({
              type: 'zoom',
              level: zoomLevel
            }));
          }
          break;
      }
    } catch (error) {
      console.error('Error parsing WebView message:', error);
    }
  };

  // Handle zoom changes
  const handleZoomChange = (newZoom: number) => {
    setZoomLevel(newZoom);
    if (webViewRef.current) {
      webViewRef.current.postMessage(JSON.stringify({
        type: 'zoom',
        level: newZoom
      }));
    }
  };

  return (
    <LinearGradient
      colors={[
        'rgb(255, 107, 157)', // candy-pink
        'rgb(168, 85, 247)',  // candy-purple
        'rgb(59, 130, 246)'   // candy-blue
      ]}
      locations={[0, 0.6, 1]}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
      style={{ flex: 1 }}
    >
      <SafeAreaView className="flex-1">
        <VStack className="flex-1">
          {/* Header - Fixed */}
          <VStack className="px-4 py-2">
            <HStack className="items-center justify-between mb-6">
              <HStack className="items-center space-x-4">
                <Pressable className="w-12 h-12 bg-white/10 border border-white/20 backdrop-blur-md rounded-xl items-center justify-center">
                  <ArrowLeft size={24} color="white" />
                </Pressable>
                <VStack className="ml-4">
                  <Text className="text-2xl font-bold text-white leading-8">Knowledge Graph</Text>
                  <Text className="text-white/60 text-base leading-6">Machine Learning Concepts</Text>
                </VStack>
              </HStack>

              <HStack className="space-x-3">
                <Pressable className="w-12 h-12 bg-white/10 border border-white/20 backdrop-blur-md rounded-xl items-center justify-center">
                  <Search size={24} color="white" />
                </Pressable>
                <Pressable className="w-12 h-12 ml-4 bg-white/10 border border-white/20 backdrop-blur-md rounded-xl items-center justify-center">
                  <Settings size={24} color="white" />
                </Pressable>
              </HStack>
            </HStack>
          </VStack>

          {/* Scrollable Content */}
          <ScrollView className="flex-1 px-4" showsVerticalScrollIndicator={false}>
            {/* Graph Stats */}
            <Box className="bg-white/10 border border-white/20 backdrop-blur-md rounded-xl p-4 mb-6">
              <HStack className="justify-around">
                <VStack className="items-center">
                  <Text className="text-lg font-bold text-candyPink mb-1">8</Text>
                  <Text className="text-white/70 text-xs">Concepts</Text>
                </VStack>
                <VStack className="items-center">
                  <Text className="text-lg font-bold text-candyPurple mb-1">12</Text>
                  <Text className="text-white/70 text-xs">Connections</Text>
                </VStack>
                <VStack className="items-center">
                  <Text className="text-lg font-bold text-candyBlue mb-1">3</Text>
                  <Text className="text-white/70 text-xs">Clusters</Text>
                </VStack>
                <VStack className="items-center">
                  <Text className="text-lg font-bold text-green-500 mb-1">2</Text>
                  <Text className="text-white/70 text-xs">Levels</Text>
                </VStack>
              </HStack>
            </Box>

            {/* Graph Controls */}
            <VStack className="space-y-4 mb-6">
              {/* Layout Toggle */}
              <HStack className="items-center justify-between">
                <HStack className="bg-white/10 border border-white/20 backdrop-blur-md p-1 rounded-xl">
                  <Button
                    action={viewMode === 'overview' ? 'candyPink' : undefined}
                    variant={viewMode === 'overview' ? 'solid' : 'outline'}
                    className="px-3 py-2 rounded-lg"
                    onPress={() => setViewMode('overview')}
                  >
                    <ButtonText className={`text-sm font-semibold ${viewMode === 'overview' ? 'text-white' : 'text-white/70'}`}>
                      Flowchart
                    </ButtonText>
                  </Button>
                  <Button
                    action={viewMode === 'detailed' ? 'candyPink' : undefined}
                    variant={viewMode === 'detailed' ? 'solid' : 'outline'}
                    className="px-3 py-2 rounded-lg"
                    onPress={() => setViewMode('detailed')}
                  >
                    <ButtonText className={`text-sm font-semibold ${viewMode === 'detailed' ? 'text-white' : 'text-white/70'}`}>
                      Mind Map
                    </ButtonText>
                  </Button>
                  <Button
                    action={viewMode === 'network' ? 'candyPink' : undefined}
                    variant={viewMode === 'network' ? 'solid' : 'outline'}
                    className="px-3 py-2 rounded-lg"
                    onPress={() => setViewMode('network')}
                  >
                    <ButtonText className={`text-sm font-semibold ${viewMode === 'network' ? 'text-white' : 'text-white/70'}`}>
                      Network
                    </ButtonText>
                  </Button>
                </HStack>

                
              </HStack>
              <HStack className="space-x-2">
                  <Button action="candyPurple" size="sm" className="py-2 px-4">
                    <ButtonText className="text-sm font-semibold">Edit Nodes</ButtonText>
                  </Button>
                  <Button action="candyBlue" size="sm" className="py-2 px-4">
                    <ButtonText className="text-sm font-semibold">Auto Layout</ButtonText>
                  </Button>
                  <Pressable className="bg-white/10 border border-white/20 backdrop-blur-md py-2 px-4 rounded-lg">
                    <Text className="text-white text-sm font-semibold">Export PNG</Text>
                  </Pressable>
                </HStack>
            </VStack>

            {/* Main Graph Container */}
            <Box className="bg-white/5 border border-white/20 backdrop-blur-md rounded-2xl overflow-hidden mb-6 relative">
              {/* Graph Header */}
              <HStack className="items-center justify-between p-4 border-b border-white/10">
                <VStack>
                  <Text className="text-lg font-bold text-white">Knowledge Network</Text>
                  <Text className="text-white/60 text-sm">Machine Learning Concepts</Text>
                </VStack>
                <HStack className="space-x-2">
                  <Pressable className="w-8 h-8 bg-white/10 rounded-lg items-center justify-center">
                    <RotateCcw size={16} color="white" />
                  </Pressable>
                  <Pressable className="w-8 h-8 bg-white/10 rounded-lg items-center justify-center">
                    <Maximize2 size={16} color="white" />
                  </Pressable>
                </HStack>
              </HStack>

              {/* Zoom Controls - Positioned */}
              <VStack className="absolute top-20 right-4 z-10 space-y-2">
                <Pressable
                  className="w-10 h-10 bg-white/10 border border-white/20 backdrop-blur-md rounded-lg items-center justify-center"
                  onPress={() => handleZoomChange(Math.min(2, zoomLevel + 0.1))}
                >
                  <Plus size={16} color="white" />
                </Pressable>
                <Pressable
                  className="w-10 h-10 bg-white/10 border border-white/20 backdrop-blur-md rounded-lg items-center justify-center"
                  onPress={() => handleZoomChange(Math.max(0.5, zoomLevel - 0.1))}
                >
                  <Minus size={16} color="white" />
                </Pressable>
                <Pressable
                  className="w-10 h-10 bg-white/10 border border-white/20 backdrop-blur-md rounded-lg items-center justify-center"
                  onPress={() => handleZoomChange(1)}
                >
                  <RotateCcw size={16} color="white" />
                </Pressable>
              </VStack>

              {/* Mermaid Graph WebView */}
              <Box className="h-[400px] bg-gradient-to-br from-white/5 to-transparent rounded-xl overflow-hidden">
                <WebView
                  ref={webViewRef}
                  source={{ html: getMermaidHTML() }}
                  style={{ backgroundColor: 'transparent' }}
                  onMessage={handleWebViewMessage}
                  javaScriptEnabled={true}
                  domStorageEnabled={true}
                  startInLoadingState={false}
                  scalesPageToFit={false}
                  scrollEnabled={false}
                  showsHorizontalScrollIndicator={false}
                  showsVerticalScrollIndicator={false}
                  originWhitelist={['*']}
                />
              </Box>

              {/* Enhanced Minimap */}
              <Box className="absolute bottom-4 right-4 w-48 h-32 bg-black/40 border border-white/20 backdrop-blur-md rounded-lg overflow-hidden">
                <VStack className="p-2">
                  <HStack className="items-center justify-between mb-2">
                    <Text className="text-white text-xs font-medium">Overview</Text>
                    <Text className="text-white/60 text-xs">{Math.round(zoomLevel * 100)}%</Text>
                  </HStack>
                  <Box className="flex-1 relative bg-white/5 rounded">
                    {/* Minimap content based on current view */}
                    {viewMode === 'overview' && (
                      <>
                        <Box className="absolute top-2 left-1/2 w-2 h-2 bg-candyPink rounded transform -translate-x-1/2" />
                        <Box className="absolute top-4 left-1/4 w-1.5 h-1.5 bg-candyPurple rounded" />
                        <Box className="absolute top-4 right-1/4 w-1.5 h-1.5 bg-candyBlue rounded" />
                        <Box className="absolute bottom-2 left-1/3 w-1 h-1 bg-candyPurple/70 rounded" />
                        <Box className="absolute bottom-2 right-1/3 w-1 h-1 bg-candyBlue/70 rounded" />
                      </>
                    )}
                    {viewMode === 'detailed' && (
                      <>
                        <Box className="absolute top-1/2 left-1/2 w-3 h-3 bg-candyPink rounded-full transform -translate-x-1/2 -translate-y-1/2" />
                        <Box className="absolute top-1/3 left-1/3 w-1.5 h-1.5 bg-candyPurple rounded" />
                        <Box className="absolute top-1/3 right-1/3 w-1.5 h-1.5 bg-candyBlue rounded" />
                        <Box className="absolute bottom-1/3 left-1/4 w-1 h-1 bg-green-500 rounded" />
                        <Box className="absolute bottom-1/3 right-1/4 w-1 h-1 bg-cyan-500 rounded" />
                      </>
                    )}
                    {viewMode === 'network' && (
                      <>
                        <Box className="absolute top-1 left-1/2 w-1.5 h-1.5 bg-candyPink rounded transform -translate-x-1/2" />
                        <Box className="absolute top-3 left-1/4 w-4 h-3 bg-candyPurple/60 rounded" />
                        <Box className="absolute top-3 right-1/4 w-4 h-3 bg-candyBlue/60 rounded" />
                        <Box className="absolute bottom-1 left-1/3 w-3 h-2 bg-cyan-500/60 rounded" />
                        <Box className="absolute bottom-1 right-1/3 w-3 h-2 bg-green-500/60 rounded" />
                      </>
                    )}

                    {/* Viewport indicator */}
                    <Box
                      className="absolute border border-candyPink/80 rounded"
                      style={{
                        width: `${Math.min(100, 100 / zoomLevel)}%`,
                        height: `${Math.min(100, 100 / zoomLevel)}%`,
                        top: '50%',
                        left: '50%',
                        transform: 'translate(-50%, -50%)'
                      }}
                    />
                  </Box>
                </VStack>
              </Box>
            </Box>

            {/* Bottom Actions */}
            <VStack className="space-y-4">
              <HStack className="justify-center space-x-4">
                <Button action="candyPink" className="py-3 px-6 rounded-xl">
                  <ButtonText className="font-semibold">Generate Learning Path</ButtonText>
                </Button>
                <Button action="candyPurple" className="py-3 px-6 rounded-xl">
                  <ButtonText className="font-semibold">Start Quiz</ButtonText>
                </Button>
              </HStack>

              <HStack className="justify-center">
                <Pressable className="bg-white/10 border border-white/20 backdrop-blur-md py-3 px-6 rounded-xl">
                  <Text className="text-white font-semibold">Back to Cards</Text>
                </Pressable>
              </HStack>
            </VStack>

            {/* Node Details Panel (when node selected) */}
            {selectedNode && (
              <Box className="bg-white/10 border border-white/20 backdrop-blur-md rounded-2xl p-6 mt-6">
                <HStack className="items-start justify-between mb-4">
                  <VStack className="flex-1">
                    <Text className="text-lg font-bold text-white mb-2">Selected: {selectedNode}</Text>
                    <Text className="text-white/70 text-sm">Explore this concept in detail</Text>
                  </VStack>
                  <Pressable onPress={() => setSelectedNode(null)}>
                    <X size={20} color="white" />
                  </Pressable>
                </HStack>
              </Box>
            )}
          </ScrollView>
        </VStack>
      </SafeAreaView>
    </LinearGradient>
  );
}

export default Graph;
