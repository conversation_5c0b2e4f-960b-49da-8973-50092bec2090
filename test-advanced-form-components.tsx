import React, { useState } from 'react';
import { View } from 'react-native';
import { 
  Slider, 
  SliderTrack, 
  SliderFilledTrack, 
  SliderThumb 
} from './components/ui/slider';
import { 
  Textarea, 
  TextareaInput 
} from './components/ui/textarea';
import { Text } from './components/ui/text';
import { Button, ButtonText } from './components/ui/button';
import { VStack } from './components/ui/vstack';
import { HStack } from './components/ui/hstack';

// Test component to validate enhanced Advanced Form Components
export function LearniScanAdvancedFormTest() {
  const [scanQuality, setScanQuality] = useState(75);
  const [brightness, setBrightness] = useState(50);
  const [contrast, setContrast] = useState(60);
  const [notes, setNotes] = useState('');
  const [feedback, setFeedback] = useState('');
  const [description, setDescription] = useState('');

  return (
    <View style={{ padding: 20, backgroundColor: '#000' }}>
      <Text color="primary" style={{ marginBottom: 20, fontSize: 18 }}>
        Enhanced Advanced Form Components Test
      </Text>

      {/* Slider Candy Color Variants */}
      <Text color="secondary" style={{ marginBottom: 15, fontSize: 16 }}>
        Slider Candy Color Variants:
      </Text>
      
      <VStack space="lg" style={{ marginBottom: 25 }}>
        {/* Candy Pink Slider */}
        <VStack space="sm">
          <HStack style={{ justifyContent: 'space-between' }}>
            <Text color="tertiary" size="sm">Scan Quality (Candy Pink):</Text>
            <Text color="candyPink" size="sm">{scanQuality}%</Text>
          </HStack>
          <Slider
            value={scanQuality}
            onChange={setScanQuality}
            minValue={0}
            maxValue={100}
            size="md"
          >
            <SliderTrack>
              <SliderFilledTrack color="candyPink" />
            </SliderTrack>
            <SliderThumb color="candyPink" />
          </Slider>
        </VStack>

        {/* Candy Purple Slider */}
        <VStack space="sm">
          <HStack style={{ justifyContent: 'space-between' }}>
            <Text color="tertiary" size="sm">Brightness (Candy Purple):</Text>
            <Text color="candyPurple" size="sm">{brightness}%</Text>
          </HStack>
          <Slider
            value={brightness}
            onChange={setBrightness}
            minValue={0}
            maxValue={100}
            size="lg"
          >
            <SliderTrack>
              <SliderFilledTrack color="candyPurple" />
            </SliderTrack>
            <SliderThumb color="candyPurple" />
          </Slider>
        </VStack>

        {/* Candy Blue Slider */}
        <VStack space="sm">
          <HStack style={{ justifyContent: 'space-between' }}>
            <Text color="tertiary" size="sm">Contrast (Candy Blue):</Text>
            <Text color="candyBlue" size="sm">{contrast}%</Text>
          </HStack>
          <Slider
            value={contrast}
            onChange={setContrast}
            minValue={0}
            maxValue={100}
            size="sm"
          >
            <SliderTrack>
              <SliderFilledTrack color="candyBlue" />
            </SliderTrack>
            <SliderThumb color="candyBlue" />
          </Slider>
        </VStack>

        {/* Default Slider */}
        <VStack space="sm">
          <HStack style={{ justifyContent: 'space-between' }}>
            <Text color="tertiary" size="sm">Default Slider:</Text>
            <Text color="primary" size="sm">50%</Text>
          </HStack>
          <Slider
            value={50}
            minValue={0}
            maxValue={100}
            size="md"
          >
            <SliderTrack>
              <SliderFilledTrack color="default" />
            </SliderTrack>
            <SliderThumb color="default" />
          </Slider>
        </VStack>
      </VStack>

      {/* Textarea Glass Variants */}
      <Text color="secondary" style={{ marginBottom: 15, fontSize: 16 }}>
        Textarea Glass Variants:
      </Text>
      
      <VStack space="lg" style={{ marginBottom: 25 }}>
        {/* Glass Textarea */}
        <VStack space="xs">
          <Text color="tertiary" size="sm">Document Notes (Glass):</Text>
          <Textarea variant="glass" size="md">
            <TextareaInput
              placeholder="Add notes about this document scan..."
              value={notes}
              onChangeText={setNotes}
            />
          </Textarea>
        </VStack>

        {/* Glass Card Textarea */}
        <VStack space="xs">
          <Text color="tertiary" size="sm">Feedback (Glass Card):</Text>
          <Textarea variant="glassCard" size="lg">
            <TextareaInput
              placeholder="Share your feedback about LearniScan..."
              value={feedback}
              onChangeText={setFeedback}
            />
          </Textarea>
        </VStack>

        {/* Default Textarea */}
        <VStack space="xs">
          <Text color="tertiary" size="sm">Description (Default):</Text>
          <Textarea variant="default" size="md">
            <TextareaInput
              placeholder="Enter document description..."
              value={description}
              onChangeText={setDescription}
            />
          </Textarea>
        </VStack>
      </VStack>

      {/* Real-World LearniScan Usage */}
      <Text color="secondary" style={{ marginBottom: 15, fontSize: 16 }}>
        Real-World LearniScan Usage:
      </Text>
      
      <VStack space="lg">
        {/* Document Enhancement Panel */}
        <VStack space="md">
          <Text color="tertiary" size="sm" style={{ fontWeight: '600' }}>
            Document Enhancement Settings:
          </Text>
          
          <VStack space="md" style={{ 
            backgroundColor: 'rgba(255, 107, 157, 0.1)', 
            padding: 16, 
            borderRadius: 12,
            borderWidth: 1,
            borderColor: 'rgba(255, 107, 157, 0.2)'
          }}>
            {/* AI Enhancement Strength */}
            <VStack space="sm">
              <HStack style={{ justifyContent: 'space-between' }}>
                <Text color="primary" size="sm">AI Enhancement</Text>
                <Text color="candyPink" size="sm">{scanQuality}%</Text>
              </HStack>
              <Slider
                value={scanQuality}
                onChange={setScanQuality}
                minValue={0}
                maxValue={100}
                size="md"
              >
                <SliderTrack>
                  <SliderFilledTrack color="candyPink" />
                </SliderTrack>
                <SliderThumb color="candyPink" />
              </Slider>
            </VStack>

            {/* Color Correction */}
            <VStack space="sm">
              <HStack style={{ justifyContent: 'space-between' }}>
                <Text color="primary" size="sm">Color Correction</Text>
                <Text color="candyPurple" size="sm">{brightness}%</Text>
              </HStack>
              <Slider
                value={brightness}
                onChange={setBrightness}
                minValue={0}
                maxValue={100}
                size="md"
              >
                <SliderTrack>
                  <SliderFilledTrack color="candyPurple" />
                </SliderTrack>
                <SliderThumb color="candyPurple" />
              </Slider>
            </VStack>

            {/* Sharpness */}
            <VStack space="sm">
              <HStack style={{ justifyContent: 'space-between' }}>
                <Text color="primary" size="sm">Sharpness</Text>
                <Text color="candyBlue" size="sm">{contrast}%</Text>
              </HStack>
              <Slider
                value={contrast}
                onChange={setContrast}
                minValue={0}
                maxValue={100}
                size="md"
              >
                <SliderTrack>
                  <SliderFilledTrack color="candyBlue" />
                </SliderTrack>
                <SliderThumb color="candyBlue" />
              </Slider>
            </VStack>
          </VStack>
        </VStack>

        {/* Document Annotation */}
        <VStack space="md">
          <Text color="tertiary" size="sm" style={{ fontWeight: '600' }}>
            Document Annotation:
          </Text>
          
          <Textarea variant="glass" size="lg">
            <TextareaInput
              placeholder="Add annotations, tags, or notes for this document. These will be saved with your scan for easy searching and organization."
              value={notes}
              onChangeText={setNotes}
              multiline
              numberOfLines={4}
            />
          </Textarea>
        </VStack>

        {/* Quick Actions */}
        <VStack space="md">
          <Text color="tertiary" size="sm" style={{ fontWeight: '600' }}>
            Quick Actions:
          </Text>
          
          <HStack space="md">
            <Button action="candyPink" size="sm" style={{ flex: 1 }}>
              <ButtonText>Apply Settings</ButtonText>
            </Button>
            
            <Button action="glass" variant="outline" size="sm" style={{ flex: 1 }}>
              <ButtonText>Reset</ButtonText>
            </Button>
            
            <Button action="candyBlue" size="sm" style={{ flex: 1 }}>
              <ButtonText>Save Preset</ButtonText>
            </Button>
          </HStack>
        </VStack>

        {/* Processing Preview */}
        <VStack space="md">
          <Text color="tertiary" size="sm" style={{ fontWeight: '600' }}>
            Processing Preview:
          </Text>
          
          <VStack space="sm" style={{ 
            backgroundColor: 'rgba(168, 85, 247, 0.1)', 
            padding: 16, 
            borderRadius: 12,
            borderWidth: 1,
            borderColor: 'rgba(168, 85, 247, 0.2)'
          }}>
            <HStack style={{ justifyContent: 'space-between' }}>
              <Text color="secondary" size="sm">Text Recognition</Text>
              <Text color="success" size="sm">98% Accuracy</Text>
            </HStack>
            
            <HStack style={{ justifyContent: 'space-between' }}>
              <Text color="secondary" size="sm">Image Quality</Text>
              <Text color="candyPink" size="sm">Excellent</Text>
            </HStack>
            
            <HStack style={{ justifyContent: 'space-between' }}>
              <Text color="secondary" size="sm">Processing Time</Text>
              <Text color="candyBlue" size="sm">2.3 seconds</Text>
            </HStack>
          </VStack>
        </VStack>
      </VStack>
    </View>
  );
}