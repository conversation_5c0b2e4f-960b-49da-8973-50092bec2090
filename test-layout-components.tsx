import React from 'react';
import { View } from 'react-native';
import { VStack } from './components/ui/vstack';
import { HStack } from './components/ui/hstack';
import { Center } from './components/ui/center';
import { Text } from './components/ui/text';
import { Button, ButtonText } from './components/ui/button';

// Test component to validate enhanced Layout Components
export function LearniScanLayoutTest() {
  return (
    <View style={{ padding: 20, backgroundColor: '#000' }}>
      <Text color="primary" style={{ marginBottom: 20, fontSize: 18 }}>
        Enhanced Layout Components Test
      </Text>

      {/* VStack Glass Variants */}
      <Text color="secondary" style={{ marginBottom: 10 }}>VStack Glass Variants:</Text>
      
      <VStack variant="glass" space="md" style={{ marginBottom: 16 }}>
        <Text color="primary">Glass VStack Container</Text>
        <Button action="candyPink" size="sm">
          <ButtonText>Button 1</ButtonText>
        </Button>
        <Button action="candyPurple" size="sm">
          <ButtonText>Button 2</ButtonText>
        </Button>
      </VStack>

      <VStack variant="glassCard" space="lg" style={{ marginBottom: 16 }}>
        <Text color="primary">Glass Card VStack (with padding)</Text>
        <Text color="secondary">Enhanced glass effect with more padding</Text>
        <Button action="candyBlue" size="sm">
          <ButtonText>Action Button</ButtonText>
        </Button>
      </VStack>

      <VStack variant="candyBorder" space="sm" style={{ marginBottom: 20 }}>
        <Text color="primary">Candy Border VStack</Text>
        <Text color="tertiary">Pink border accent container</Text>
      </VStack>

      {/* HStack Glass Variants */}
      <Text color="secondary" style={{ marginBottom: 10 }}>HStack Glass Variants:</Text>
      
      <HStack variant="glass" space="md" style={{ marginBottom: 16 }}>
        <Button action="candyPink" size="sm">
          <ButtonText>Left</ButtonText>
        </Button>
        <Button action="candyPurple" size="sm">
          <ButtonText>Center</ButtonText>
        </Button>
        <Button action="candyBlue" size="sm">
          <ButtonText>Right</ButtonText>
        </Button>
      </HStack>

      <HStack variant="glassCard" space="lg" style={{ marginBottom: 20 }}>
        <Text color="primary">Glass Card HStack</Text>
        <Button action="glass" size="sm">
          <ButtonText>Glass Button</ButtonText>
        </Button>
      </HStack>

      {/* Center Glass Variants */}
      <Text color="secondary" style={{ marginBottom: 10 }}>Center Glass Variants:</Text>
      
      <Center variant="glass" style={{ height: 100, marginBottom: 16 }}>
        <Text color="primary">Centered Glass Container</Text>
        <Text color="secondary">Perfect centering with glass effect</Text>
      </Center>

      <Center variant="glassCard" style={{ height: 120, marginBottom: 16 }}>
        <Text color="primary">Centered Glass Card</Text>
        <Button action="candyPink" size="sm">
          <ButtonText>Centered Action</ButtonText>
        </Button>
      </Center>

      <Center variant="candyBorder" style={{ height: 80 }}>
        <Text color="candyPink">Candy Border Center</Text>
      </Center>
    </View>
  );
}