import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { appwriteService } from '@/lib/services/appwrite.service';
import type { AuthUser, AppwriteUser } from '@/types/appwrite';

interface AuthContextType {
  // Auth state
  user: AuthUser | null;
  userProfile: AppwriteUser | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  isAnonymous: boolean;

  // Auth actions
  signIn: (email: string, password: string) => Promise<AuthUser>;
  signUp: (email: string, password: string, name: string) => Promise<AuthUser>;
  signOut: () => Promise<void>;
  signOutFromAllDevices: () => Promise<void>;
  createAnonymousSession: () => Promise<AuthUser>;
  
  // Profile actions
  updateProfile: (data: { name?: string }) => Promise<AuthUser>;
  updateEmail: (email: string, password: string) => Promise<AuthUser>;
  changePassword: (newPassword: string, oldPassword: string) => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
  
  // User profile actions
  updateUserProfile: (data: Partial<AppwriteUser>) => Promise<AppwriteUser>;
  refreshUserProfile: () => Promise<AppwriteUser | null>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<AuthUser | null>(null);
  const [userProfile, setUserProfile] = useState<AppwriteUser | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Computed properties
  const isAuthenticated = user !== null;
  const isAnonymous = user?.email === '' || user?.email === undefined;

  useEffect(() => {
    initializeAuth();
  }, []);

  const initializeAuth = async () => {
    try {
      setIsLoading(true);
      
      // Check if user is already authenticated
      const currentUser = await appwriteService.auth.getCurrentUser();
      
      if (currentUser) {
        setUser(currentUser);
        
        // Load user profile
        try {
          const profile = await appwriteService.database.getUserProfile(currentUser.$id);
          setUserProfile(profile);
        } catch (error) {
          // Profile might not exist, create it
          try {
            const newProfile = await appwriteService.initializeUser(currentUser);
            setUserProfile(newProfile);
          } catch (profileError) {
            console.error('Failed to create user profile:', profileError);
          }
        }
      }
    } catch (error) {
      console.log('No active session found');
      setUser(null);
      setUserProfile(null);
    } finally {
      setIsLoading(false);
    }
  };

  const signIn = async (email: string, password: string): Promise<AuthUser> => {
    try {
      setIsLoading(true);
      const authUser = await appwriteService.auth.signIn(email, password);
      setUser(authUser);
      
      // Load or create user profile
      try {
        const profile = await appwriteService.database.getUserProfile(authUser.$id);
        setUserProfile(profile);
      } catch (error) {
        const newProfile = await appwriteService.initializeUser(authUser);
        setUserProfile(newProfile);
      }
      
      return authUser;
    } catch (error) {
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const signUp = async (email: string, password: string, name: string): Promise<AuthUser> => {
    try {
      setIsLoading(true);
      const authUser = await appwriteService.auth.signUp(email, password, name);
      setUser(authUser);
      
      // Create user profile
      const newProfile = await appwriteService.initializeUser(authUser);
      setUserProfile(newProfile);
      
      return authUser;
    } catch (error) {
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const signOut = async (): Promise<void> => {
    try {
      setIsLoading(true);
      await appwriteService.auth.signOut();
      setUser(null);
      setUserProfile(null);
    } catch (error) {
      // Even if server sign out fails, clear local state
      setUser(null);
      setUserProfile(null);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const signOutFromAllDevices = async (): Promise<void> => {
    try {
      setIsLoading(true);
      await appwriteService.auth.signOutFromAllDevices();
      setUser(null);
      setUserProfile(null);
    } catch (error) {
      setUser(null);
      setUserProfile(null);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const createAnonymousSession = async (): Promise<AuthUser> => {
    try {
      setIsLoading(true);
      const authUser = await appwriteService.auth.createAnonymousSession();
      setUser(authUser);
      
      // Anonymous users don't get full profiles, but we can create a minimal one
      const anonymousProfile: Partial<AppwriteUser> = {
        email: '',
        name: 'Guest User',
        preferences: {
          language: 'en',
          theme: 'light',
          notifications: false,
        },
        learningStats: {
          totalCards: 0,
          totalScans: 0,
          streakDays: 0,
          lastActive: new Date().toISOString(),
        },
      };
      
      // Don't save anonymous profile to database
      setUserProfile(anonymousProfile as AppwriteUser);
      
      return authUser;
    } catch (error) {
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const updateProfile = async (data: { name?: string }): Promise<AuthUser> => {
    try {
      const updatedUser = await appwriteService.auth.updateProfile(data);
      setUser(updatedUser);
      
      // Update user profile if it exists
      if (userProfile && !isAnonymous) {
        const updatedProfile = await appwriteService.database.updateUserProfile(
          updatedUser.$id,
          { name: data.name }
        );
        setUserProfile(updatedProfile);
      }
      
      return updatedUser;
    } catch (error) {
      throw error;
    }
  };

  const updateEmail = async (email: string, password: string): Promise<AuthUser> => {
    try {
      const updatedUser = await appwriteService.auth.updateEmail(email, password);
      setUser(updatedUser);
      
      // Update user profile if it exists
      if (userProfile && !isAnonymous) {
        const updatedProfile = await appwriteService.database.updateUserProfile(
          updatedUser.$id,
          { email }
        );
        setUserProfile(updatedProfile);
      }
      
      return updatedUser;
    } catch (error) {
      throw error;
    }
  };

  const changePassword = async (newPassword: string, oldPassword: string): Promise<void> => {
    try {
      await appwriteService.auth.changePassword(newPassword, oldPassword);
    } catch (error) {
      throw error;
    }
  };

  const resetPassword = async (email: string): Promise<void> => {
    try {
      await appwriteService.auth.resetPassword(email);
    } catch (error) {
      throw error;
    }
  };

  const updateUserProfile = async (data: Partial<AppwriteUser>): Promise<AppwriteUser> => {
    if (!user || isAnonymous) {
      throw new Error('User must be authenticated to update profile');
    }

    try {
      const updatedProfile = await appwriteService.database.updateUserProfile(user.$id, data);
      setUserProfile(updatedProfile);
      return updatedProfile;
    } catch (error) {
      throw error;
    }
  };

  const refreshUserProfile = async (): Promise<AppwriteUser | null> => {
    if (!user || isAnonymous) {
      return null;
    }

    try {
      const profile = await appwriteService.database.getUserProfile(user.$id);
      setUserProfile(profile);
      return profile;
    } catch (error) {
      console.error('Failed to refresh user profile:', error);
      return null;
    }
  };

  const contextValue: AuthContextType = {
    // State
    user,
    userProfile,
    isLoading,
    isAuthenticated,
    isAnonymous,

    // Auth actions
    signIn,
    signUp,
    signOut,
    signOutFromAllDevices,
    createAnonymousSession,

    // Profile actions
    updateProfile,
    updateEmail,
    changePassword,
    resetPassword,

    // User profile actions
    updateUserProfile,
    refreshUserProfile,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export default AuthContext;
