import { ID, OAuthProvider } from 'react-native-appwrite';
import { account } from '@/lib/config/appwrite';
import type { AuthUser, AuthSession, AppwriteError } from '@/types/appwrite';
import AsyncStorage from '@react-native-async-storage/async-storage';

export class AuthService {
  private readonly USER_KEY = 'appwrite_user';
  private readonly SESSION_KEY = 'appwrite_session';

  // Email/Password Authentication
  async signUp(email: string, password: string, name: string): Promise<AuthUser> {
    try {
      // Create user account
      const user = await account.create(ID.unique(), email, password, name);
      
      // Create session
      await account.createEmailPasswordSession(email, password);
      
      // Get current user with session
      const currentUser = await this.getCurrentUser();
      
      if (currentUser) {
        await this.storeUserLocally(currentUser);
        return currentUser;
      }
      
      throw new Error('Failed to get user after sign up');
    } catch (error) {
      throw this.handleError(error, 'Sign up failed');
    }
  }

  async signIn(email: string, password: string): Promise<AuthUser> {
    try {
      // Create session
      await account.createEmailPasswordSession(email, password);
      
      // Get current user
      const user = await this.getCurrentUser();
      
      if (user) {
        await this.storeUserLocally(user);
        return user;
      }
      
      throw new Error('Failed to get user after sign in');
    } catch (error) {
      throw this.handleError(error, 'Sign in failed');
    }
  }

  // OAuth Authentication
  async signInWithOAuth(provider: OAuthProvider | string): Promise<void> {
    try {
      await account.createOAuth2Session(
        provider,
        'learni-scan://auth/success',
        'learni-scan://auth/failure'
      );
    } catch (error) {
      throw this.handleError(error, 'OAuth sign in failed');
    }
  }

  // Anonymous Session
  async createAnonymousSession(): Promise<AuthUser> {
    try {
      const session = await account.createAnonymousSession();
      const user = await this.getCurrentUser();
      
      if (user) {
        await this.storeUserLocally(user);
        return user;
      }
      
      throw new Error('Failed to create anonymous session');
    } catch (error) {
      throw this.handleError(error, 'Anonymous session creation failed');
    }
  }

  // Session Management
  async getCurrentUser(): Promise<AuthUser | null> {
    try {
      const user = await account.get();
      return user as AuthUser;
    } catch (error) {
      // If no active session, try to get from local storage
      return await this.getUserFromLocal();
    }
  }

  async getCurrentSession(): Promise<AuthSession | null> {
    try {
      const session = await account.getSession('current');
      return session as AuthSession;
    } catch (error) {
      return null;
    }
  }

  async listSessions(): Promise<AuthSession[]> {
    try {
      const sessions = await account.listSessions();
      return sessions.sessions as AuthSession[];
    } catch (error) {
      throw this.handleError(error, 'Failed to list sessions');
    }
  }

  async signOut(): Promise<void> {
    try {
      await account.deleteSession('current');
      await this.clearLocalStorage();
    } catch (error) {
      // Even if server sign out fails, clear local storage
      await this.clearLocalStorage();
      throw this.handleError(error, 'Sign out failed');
    }
  }

  async signOutFromAllDevices(): Promise<void> {
    try {
      await account.deleteSessions();
      await this.clearLocalStorage();
    } catch (error) {
      await this.clearLocalStorage();
      throw this.handleError(error, 'Sign out from all devices failed');
    }
  }

  // Account Management
  async updateProfile(data: { name?: string }): Promise<AuthUser> {
    try {
      if (data.name) {
        await account.updateName(data.name);
      }
      
      const user = await this.getCurrentUser();
      if (user) {
        await this.storeUserLocally(user);
        return user;
      }
      
      throw new Error('Failed to get updated user');
    } catch (error) {
      throw this.handleError(error, 'Profile update failed');
    }
  }

  async updateEmail(email: string, password: string): Promise<AuthUser> {
    try {
      await account.updateEmail(email, password);
      
      const user = await this.getCurrentUser();
      if (user) {
        await this.storeUserLocally(user);
        return user;
      }
      
      throw new Error('Failed to get updated user');
    } catch (error) {
      throw this.handleError(error, 'Email update failed');
    }
  }

  async changePassword(newPassword: string, oldPassword: string): Promise<void> {
    try {
      await account.updatePassword(newPassword, oldPassword);
    } catch (error) {
      throw this.handleError(error, 'Password change failed');
    }
  }

  async resetPassword(email: string): Promise<void> {
    try {
      await account.createRecovery(
        email,
        'learni-scan://auth/reset-password'
      );
    } catch (error) {
      throw this.handleError(error, 'Password reset failed');
    }
  }

  async updatePasswordRecovery(userId: string, secret: string, password: string): Promise<void> {
    try {
      await account.updateRecovery(userId, secret, password);
    } catch (error) {
      throw this.handleError(error, 'Password recovery update failed');
    }
  }

  // Email Verification
  async createEmailVerification(): Promise<void> {
    try {
      await account.createVerification('learni-scan://auth/verify-email');
    } catch (error) {
      throw this.handleError(error, 'Email verification creation failed');
    }
  }

  async verifyEmail(userId: string, secret: string): Promise<void> {
    try {
      await account.updateVerification(userId, secret);
    } catch (error) {
      throw this.handleError(error, 'Email verification failed');
    }
  }

  // User Preferences
  async updatePreferences(prefs: Record<string, any>): Promise<AuthUser> {
    try {
      await account.updatePrefs(prefs);
      
      const user = await this.getCurrentUser();
      if (user) {
        await this.storeUserLocally(user);
        return user;
      }
      
      throw new Error('Failed to get updated user');
    } catch (error) {
      throw this.handleError(error, 'Preferences update failed');
    }
  }

  // Local Storage Management
  private async storeUserLocally(user: AuthUser): Promise<void> {
    try {
      await AsyncStorage.setItem(this.USER_KEY, JSON.stringify(user));
    } catch (error) {
      console.warn('Failed to store user locally:', error);
    }
  }

  private async getUserFromLocal(): Promise<AuthUser | null> {
    try {
      const userData = await AsyncStorage.getItem(this.USER_KEY);
      return userData ? JSON.parse(userData) : null;
    } catch (error) {
      console.warn('Failed to get user from local storage:', error);
      return null;
    }
  }

  private async clearLocalStorage(): Promise<void> {
    try {
      await AsyncStorage.multiRemove([this.USER_KEY, this.SESSION_KEY]);
    } catch (error) {
      console.warn('Failed to clear local storage:', error);
    }
  }

  // Error Handling
  private handleError(error: any, defaultMessage: string): Error {
    if (error.code) {
      const appwriteError = error as AppwriteError;
      switch (appwriteError.code) {
        case 401:
          return new Error('Authentication required. Please sign in again.');
        case 403:
          return new Error('Access denied. You don\'t have permission for this action.');
        case 409:
          return new Error('User already exists with this email.');
        case 429:
          return new Error('Too many requests. Please try again later.');
        case 500:
          return new Error('Server error. Please try again later.');
        case 503:
          return new Error('Service unavailable. Please check your connection.');
        default:
          return new Error(appwriteError.message || defaultMessage);
      }
    }
    
    return new Error(error.message || defaultMessage);
  }

  // Utility Methods
  async isAuthenticated(): Promise<boolean> {
    try {
      const user = await this.getCurrentUser();
      return user !== null;
    } catch (error) {
      return false;
    }
  }

  async getAuthToken(): Promise<string | null> {
    try {
      const session = await this.getCurrentSession();
      return session?.$id || null;
    } catch (error) {
      return null;
    }
  }
}

export const authService = new AuthService();
