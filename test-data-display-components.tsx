import React from 'react';
import { View } from 'react-native';
import { Badge, BadgeText, BadgeIcon } from './components/ui/badge';
import { Progress, ProgressFilledTrack } from './components/ui/progress';
import { Avatar, AvatarBadge, AvatarFallbackText, AvatarImage, AvatarGroup } from './components/ui/avatar';
import { Text } from './components/ui/text';
import { VStack } from './components/ui/vstack';
import { HStack } from './components/ui/hstack';
import { Button, ButtonText } from './components/ui/button';
import { CheckIcon, StarIcon, AlertCircleIcon, InfoIcon } from 'lucide-react-native';

// Test component to validate enhanced Data Display Components
export function LearniScanDataDisplayTest() {
  return (
    <View style={{ padding: 20, backgroundColor: '#000' }}>
      <Text color="primary" style={{ marginBottom: 20, fontSize: 18 }}>
        Enhanced Data Display Components Test
      </Text>

      {/* Badge Candy Color Variants */}
      <Text color="secondary" style={{ marginBottom: 15, fontSize: 16 }}>
        Badge Candy Color Variants:
      </Text>
      
      <VStack space="md" style={{ marginBottom: 25 }}>
        <HStack space="md" style={{ flexWrap: 'wrap' }}>
          <Badge action="candyPink" variant="solid" size="sm">
            <BadgeText>New</BadgeText>
          </Badge>
          
          <Badge action="candyPurple" variant="solid" size="md">
            <BadgeText>Premium</BadgeText>
          </Badge>
          
          <Badge action="candyBlue" variant="solid" size="lg">
            <BadgeText>Pro</BadgeText>
          </Badge>
        </HStack>

        <HStack space="md" style={{ flexWrap: 'wrap' }}>
          <Badge action="candyPink" variant="solid" size="md">
            <BadgeIcon as={StarIcon} />
            <BadgeText>Featured</BadgeText>
          </Badge>
          
          <Badge action="candyPurple" variant="solid" size="md">
            <BadgeIcon as={CheckIcon} />
            <BadgeText>Verified</BadgeText>
          </Badge>
          
          <Badge action="candyBlue" variant="solid" size="md">
            <BadgeIcon as={InfoIcon} />
            <BadgeText>Info</BadgeText>
          </Badge>
        </HStack>
      </VStack>

      {/* Badge Glass Effect Variants */}
      <Text color="secondary" style={{ marginBottom: 15, fontSize: 16 }}>
        Badge Glass Effect Variants:
      </Text>
      
      <VStack space="md" style={{ marginBottom: 25 }}>
        <HStack space="md" style={{ flexWrap: 'wrap' }}>
          <Badge action="candyPink" variant="glass" size="md">
            <BadgeText>Glass Pink</BadgeText>
          </Badge>
          
          <Badge action="candyPurple" variant="glassCard" size="md">
            <BadgeText>Glass Card Purple</BadgeText>
          </Badge>
          
          <Badge action="candyBlue" variant="glass" size="md">
            <BadgeIcon as={StarIcon} />
            <BadgeText>Glass Blue</BadgeText>
          </Badge>
        </HStack>
      </VStack>

      {/* Badge Outline Variants */}
      <Text color="secondary" style={{ marginBottom: 15, fontSize: 16 }}>
        Badge Outline Variants:
      </Text>
      
      <VStack space="md" style={{ marginBottom: 25 }}>
        <HStack space="md" style={{ flexWrap: 'wrap' }}>
          <Badge action="candyPink" variant="outline" size="sm">
            <BadgeText>Outline Pink</BadgeText>
          </Badge>
          
          <Badge action="candyPurple" variant="outline" size="md">
            <BadgeText>Outline Purple</BadgeText>
          </Badge>
          
          <Badge action="candyBlue" variant="outline" size="lg">
            <BadgeText>Outline Blue</BadgeText>
          </Badge>
        </HStack>
      </VStack>

      {/* Existing Semantic Badge Variants */}
      <Text color="secondary" style={{ marginBottom: 15, fontSize: 16 }}>
        Existing Semantic Badge Variants:
      </Text>
      
      <VStack space="md" style={{ marginBottom: 25 }}>
        <HStack space="md" style={{ flexWrap: 'wrap' }}>
          <Badge action="success" variant="solid" size="md">
            <BadgeIcon as={CheckIcon} />
            <BadgeText>Success</BadgeText>
          </Badge>
          
          <Badge action="error" variant="solid" size="md">
            <BadgeIcon as={AlertCircleIcon} />
            <BadgeText>Error</BadgeText>
          </Badge>
          
          <Badge action="warning" variant="solid" size="md">
            <BadgeText>Warning</BadgeText>
          </Badge>
          
          <Badge action="info" variant="solid" size="md">
            <BadgeText>Info</BadgeText>
          </Badge>
        </HStack>
      </VStack>

      {/* Real-World Usage Examples */}
      <Text color="secondary" style={{ marginBottom: 15, fontSize: 16 }}>
        Real-World LearniScan Usage:
      </Text>
      
      <VStack space="lg" style={{ marginBottom: 25 }}>
        {/* Document Status Badges */}
        <VStack space="sm">
          <Text color="tertiary" size="sm">Document Status:</Text>
          <HStack space="sm" style={{ flexWrap: 'wrap' }}>
            <Badge action="candyBlue" variant="solid" size="sm">
              <BadgeText>Scanning</BadgeText>
            </Badge>
            <Badge action="candyPurple" variant="solid" size="sm">
              <BadgeText>Processing</BadgeText>
            </Badge>
            <Badge action="success" variant="solid" size="sm">
              <BadgeIcon as={CheckIcon} />
              <BadgeText>Complete</BadgeText>
            </Badge>
          </HStack>
        </VStack>

        {/* Feature Badges */}
        <VStack space="sm">
          <Text color="tertiary" size="sm">Feature Badges:</Text>
          <HStack space="sm" style={{ flexWrap: 'wrap' }}>
            <Badge action="candyPink" variant="glass" size="md">
              <BadgeIcon as={StarIcon} />
              <BadgeText>AI Enhanced</BadgeText>
            </Badge>
            <Badge action="candyPurple" variant="glassCard" size="md">
              <BadgeText>Premium</BadgeText>
            </Badge>
            <Badge action="candyBlue" variant="outline" size="md">
              <BadgeText>Beta</BadgeText>
            </Badge>
          </HStack>
        </VStack>

        {/* Quality Badges */}
        <VStack space="sm">
          <Text color="tertiary" size="sm">Scan Quality:</Text>
          <HStack space="sm" style={{ flexWrap: 'wrap' }}>
            <Badge action="success" variant="solid" size="sm">
              <BadgeText>HD</BadgeText>
            </Badge>
            <Badge action="candyPurple" variant="solid" size="sm">
              <BadgeText>4K</BadgeText>
            </Badge>
            <Badge action="candyPink" variant="solid" size="sm">
              <BadgeIcon as={StarIcon} />
              <BadgeText>Ultra</BadgeText>
            </Badge>
          </HStack>
        </VStack>
      </VStack>

      {/* Avatar Component Examples */}
      <Text color="secondary" style={{ marginBottom: 15, fontSize: 16 }}>
        Avatar Component Examples:
      </Text>
      
      <VStack space="lg" style={{ marginBottom: 25 }}>
        {/* Avatar Candy Color Variants */}
        <VStack space="sm">
          <Text color="tertiary" size="sm">Candy Color Avatars:</Text>
          
          <HStack space="md" style={{ alignItems: 'center' }}>
            <Avatar variant="candyPink" size="md">
              <AvatarFallbackText>JP</AvatarFallbackText>
            </Avatar>
            
            <Avatar variant="candyPurple" size="md">
              <AvatarFallbackText>AS</AvatarFallbackText>
            </Avatar>
            
            <Avatar variant="candyBlue" size="md">
              <AvatarFallbackText>LM</AvatarFallbackText>
            </Avatar>
            
            <Avatar variant="default" size="md">
              <AvatarFallbackText>DF</AvatarFallbackText>
            </Avatar>
          </HStack>
        </VStack>

        {/* Avatar Glass Effect Variants */}
        <VStack space="sm">
          <Text color="tertiary" size="sm">Glass Effect Avatars:</Text>
          
          <HStack space="md" style={{ alignItems: 'center' }}>
            <Avatar variant="glass" size="lg">
              <AvatarFallbackText>GL</AvatarFallbackText>
            </Avatar>
            
            <Avatar variant="glassCard" size="lg">
              <AvatarFallbackText>GC</AvatarFallbackText>
            </Avatar>
          </HStack>
        </VStack>

        {/* Avatar Size Variants */}
        <VStack space="sm">
          <Text color="tertiary" size="sm">Avatar Sizes:</Text>
          
          <HStack space="md" style={{ alignItems: 'center' }}>
            <Avatar variant="candyPink" size="xs">
              <AvatarFallbackText>XS</AvatarFallbackText>
            </Avatar>
            
            <Avatar variant="candyPurple" size="sm">
              <AvatarFallbackText>SM</AvatarFallbackText>
            </Avatar>
            
            <Avatar variant="candyBlue" size="md">
              <AvatarFallbackText>MD</AvatarFallbackText>
            </Avatar>
            
            <Avatar variant="candyPink" size="lg">
              <AvatarFallbackText>LG</AvatarFallbackText>
            </Avatar>
            
            <Avatar variant="candyPurple" size="xl">
              <AvatarFallbackText>XL</AvatarFallbackText>
            </Avatar>
          </HStack>
        </VStack>

        {/* Avatar with Badges */}
        <VStack space="sm">
          <Text color="tertiary" size="sm">Avatars with Status Badges:</Text>
          
          <HStack space="lg" style={{ alignItems: 'center' }}>
            <Avatar variant="candyPink" size="lg">
              <AvatarFallbackText>ON</AvatarFallbackText>
              <AvatarBadge variant="success" />
            </Avatar>
            
            <Avatar variant="candyPurple" size="lg">
              <AvatarFallbackText>AW</AvatarFallbackText>
              <AvatarBadge variant="warning" />
            </Avatar>
            
            <Avatar variant="candyBlue" size="lg">
              <AvatarFallbackText>OF</AvatarFallbackText>
              <AvatarBadge variant="error" />
            </Avatar>
            
            <Avatar variant="glass" size="lg">
              <AvatarFallbackText>PR</AvatarFallbackText>
              <AvatarBadge variant="candyPink" />
            </Avatar>
          </HStack>
        </VStack>

        {/* Avatar Group */}
        <VStack space="sm">
          <Text color="tertiary" size="sm">Avatar Group:</Text>
          
          <AvatarGroup>
            <Avatar variant="candyPink" size="md">
              <AvatarFallbackText>U1</AvatarFallbackText>
            </Avatar>
            <Avatar variant="candyPurple" size="md">
              <AvatarFallbackText>U2</AvatarFallbackText>
            </Avatar>
            <Avatar variant="candyBlue" size="md">
              <AvatarFallbackText>U3</AvatarFallbackText>
            </Avatar>
            <Avatar variant="glass" size="md">
              <AvatarFallbackText>U4</AvatarFallbackText>
            </Avatar>
            <Avatar variant="default" size="md">
              <AvatarFallbackText>+5</AvatarFallbackText>
            </Avatar>
          </AvatarGroup>
        </VStack>

        {/* Real-World LearniScan Usage */}
        <VStack space="sm">
          <Text color="tertiary" size="sm">LearniScan User Profiles:</Text>
          
          <VStack space="md">
            <HStack space="md" style={{ alignItems: 'center' }}>
              <Avatar variant="candyPink" size="md">
                <AvatarFallbackText>JS</AvatarFallbackText>
                <AvatarBadge variant="success" />
              </Avatar>
              <VStack space="xs">
                <Text color="primary" size="sm">John Smith</Text>
                <Text color="tertiary" size="xs">Premium User • Online</Text>
              </VStack>
            </HStack>
            
            <HStack space="md" style={{ alignItems: 'center' }}>
              <Avatar variant="glass" size="md">
                <AvatarFallbackText>AI</AvatarFallbackText>
                <AvatarBadge variant="candyBlue" />
              </Avatar>
              <VStack space="xs">
                <Text color="primary" size="sm">AI Assistant</Text>
                <Text color="tertiary" size="xs">LearniScan Bot • Active</Text>
              </VStack>
            </HStack>
            
            <HStack space="md" style={{ alignItems: 'center' }}>
              <Avatar variant="candyPurple" size="md">
                <AvatarFallbackText>EM</AvatarFallbackText>
                <AvatarBadge variant="warning" />
              </Avatar>
              <VStack space="xs">
                <Text color="primary" size="sm">Emma Martinez</Text>
                <Text color="tertiary" size="xs">Free User • Away</Text>
              </VStack>
            </HStack>
          </VStack>
        </VStack>
      </VStack>

      {/* Interactive Badge Examples */}
      <Text color="secondary" style={{ marginBottom: 15, fontSize: 16 }}>
        Interactive Badge Examples:
      </Text>
      
      <VStack space="md">
        <HStack space="md" style={{ alignItems: 'center' }}>
          <Button action="candyPink" size="sm">
            <ButtonText>Scan Document</ButtonText>
          </Button>
          <Badge action="candyPink" variant="glass" size="sm">
            <BadgeText>New Feature</BadgeText>
          </Badge>
        </HStack>

        <HStack space="md" style={{ alignItems: 'center' }}>
          <Button action="candyPurple" size="sm">
            <ButtonText>Premium Scan</ButtonText>
          </Button>
          <Badge action="candyPurple" variant="solid" size="sm">
            <BadgeIcon as={StarIcon} />
            <BadgeText>Pro</BadgeText>
          </Badge>
        </HStack>

        <HStack space="md" style={{ alignItems: 'center' }}>
          <Button action="candyBlue" size="sm">
            <ButtonText>AI Analysis</ButtonText>
          </Button>
          <Badge action="candyBlue" variant="glassCard" size="sm">
            <BadgeText>Beta</BadgeText>
          </Badge>
        </HStack>
      </VStack>
    </View>
  );
}