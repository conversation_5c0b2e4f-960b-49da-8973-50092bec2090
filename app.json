{"expo": {"name": "pocket.manus", "slug": "pocket-manus", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "myapp", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "bundleIdentifier": "com.barney666.pocket.manus", "infoPlist": {"NSPhotoLibraryUsageDescription": "This app needs access to your photo library to let you share images and customize your profile."}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#000000"}, "package": "com.barney666.pocket.manus"}, "web": {"bundler": "metro", "output": "server", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-font", "expo-router", "expo-localization", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#000000"}], ["expo-notifications", {"icon": "./assets/images/icon.png", "color": "#ffffff", "defaultChannel": "default", "enableBackgroundRemoteNotifications": false}], ["expo-camera", {"cameraPermission": "Allow $(PRODUCT_NAME) to access your camera", "microphonePermission": "Allow $(PRODUCT_NAME) to access your microphone", "recordAudioAndroid": true}]], "experiments": {"typedRoutes": true}}}