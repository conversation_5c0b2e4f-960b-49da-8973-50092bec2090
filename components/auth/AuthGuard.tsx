import React, { useEffect } from 'react';
import { useRouter, useSegments } from 'expo-router';
import { View } from 'react-native';
import { useAuth } from '@/lib/contexts/AuthContext';
import { Spinner } from '@/components/ui/spinner';
import { Box } from '@/components/ui/box';
import { Text } from '@/components/ui/text';
import { VStack } from '@/components/ui/vstack';

interface AuthGuardProps {
  children: React.ReactNode;
}

export const AuthGuard: React.FC<AuthGuardProps> = ({ children }) => {
  const { isAuthenticated, isLoading, user } = useAuth();
  const router = useRouter();
  const segments = useSegments();

  useEffect(() => {
    if (isLoading) return; // Don't do anything while loading

    const inAuthGroup = segments[0] === 'auth';
    const inTabsGroup = segments[0] === '(tabs)';
    const inKnowledgeGroup = segments[0] === '(knowledge)';
    const inProtectedRoute = inTabsGroup || inKnowledgeGroup;

    if (!isAuthenticated && inProtectedRoute) {
      // User is not authenticated but trying to access protected route
      router.replace('/auth/login');
    } else if (isAuthenticated && inAuthGroup) {
      // User is authenticated but on auth screen
      router.replace('/(tabs)/home');
    }
  }, [isAuthenticated, isLoading, segments]);

  // Show loading screen while checking authentication
  if (isLoading) {
    return (
      <Box className="flex-1 justify-center items-center bg-background">
        <VStack space="lg" className="items-center">
          <Spinner size="large" />
          <Text className="text-lg text-typography-600">
            Loading...
          </Text>
        </VStack>
      </Box>
    );
  }

  return <>{children}</>;
};

export default AuthGuard;
