import React, { useState, useEffect } from 'react';
import { View, Alert, KeyboardAvoidingView, Platform, SafeAreaView } from 'react-native';
import { useRouter } from 'expo-router';
import { OAuthProvider } from 'react-native-appwrite';
import { FontAwesome6 } from '@expo/vector-icons';

// Gluestack UI Components
import { Box } from '@/components/ui/box';
import { Text } from '@/components/ui/text';
import { Button, ButtonText } from '@/components/ui/button';
import { Input, InputField } from '@/components/ui/input';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Spinner } from '@/components/ui/spinner';
import { Pressable } from '@/components/ui/pressable';

// AppWrite Service
import { appwriteService } from '@/lib/services/appwrite.service';
import type { AuthUser } from '@/types/appwrite';

interface AppwriteAuthScreenProps {
  onAuthSuccess?: (user: AuthUser) => void;
  initialMode?: 'signin' | 'signup';
}

export const AppwriteAuthScreen: React.FC<AppwriteAuthScreenProps> = ({
  onAuthSuccess,
  initialMode = 'signin'
}) => {
  const router = useRouter();
  const [mode, setMode] = useState<'signin' | 'signup'>(initialMode);
  const [loading, setLoading] = useState(false);
  const [oauthLoading, setOauthLoading] = useState<string | null>(null);
  
  // Form state
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [name, setName] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');

  // Form validation
  const [errors, setErrors] = useState<{
    email?: string;
    password?: string;
    name?: string;
    confirmPassword?: string;
  }>({});

  useEffect(() => {
    // Check if user is already authenticated
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      const user = await appwriteService.auth.getCurrentUser();
      if (user && onAuthSuccess) {
        onAuthSuccess(user);
      }
    } catch (error) {
      // User not authenticated, continue with auth flow
    }
  };

  const validateForm = (): boolean => {
    const newErrors: typeof errors = {};

    // Email validation
    if (!email) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      newErrors.email = 'Please enter a valid email';
    }

    // Password validation
    if (!password) {
      newErrors.password = 'Password is required';
    } else if (password.length < 8) {
      newErrors.password = 'Password must be at least 8 characters';
    }

    // Name validation for signup
    if (mode === 'signup') {
      if (!name) {
        newErrors.name = 'Name is required';
      } else if (name.length < 2) {
        newErrors.name = 'Name must be at least 2 characters';
      }

      // Confirm password validation
      if (!confirmPassword) {
        newErrors.confirmPassword = 'Please confirm your password';
      } else if (password !== confirmPassword) {
        newErrors.confirmPassword = 'Passwords do not match';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleEmailAuth = async () => {
    if (!validateForm()) return;

    setLoading(true);
    try {
      let user: AuthUser;

      if (mode === 'signup') {
        user = await appwriteService.auth.signUp(email, password, name);
        Alert.alert(
          'Account Created',
          'Your account has been created successfully. Please check your email for verification.',
          [{ text: 'OK' }]
        );
      } else {
        user = await appwriteService.auth.signIn(email, password);
      }

      // Initialize user profile
      await appwriteService.initializeUser(user);

      if (onAuthSuccess) {
        onAuthSuccess(user);
      } else {
        router.replace('/(tabs)/home');
      }
    } catch (error) {
      Alert.alert(
        'Authentication Error',
        error.message || 'An error occurred during authentication',
        [{ text: 'OK' }]
      );
    } finally {
      setLoading(false);
    }
  };

  const handleOAuthSignIn = async (provider: OAuthProvider | string, providerName: string) => {
    setOauthLoading(providerName);
    try {
      await appwriteService.auth.signInWithOAuth(provider);
      // OAuth success will be handled by the redirect URL
    } catch (error) {
      Alert.alert(
        'OAuth Error',
        `Failed to sign in with ${providerName}. Please try again.`,
        [{ text: 'OK' }]
      );
    } finally {
      setOauthLoading(null);
    }
  };

  const handleAnonymousSignIn = async () => {
    setLoading(true);
    try {
      const user = await appwriteService.auth.createAnonymousSession();
      
      if (onAuthSuccess) {
        onAuthSuccess(user);
      } else {
        router.replace('/(tabs)/home');
      }
    } catch (error) {
      Alert.alert(
        'Anonymous Sign In Error',
        'Failed to create anonymous session. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setLoading(false);
    }
  };

  const handleForgotPassword = async () => {
    if (!email) {
      Alert.alert('Email Required', 'Please enter your email address first.');
      return;
    }

    try {
      await appwriteService.auth.resetPassword(email);
      Alert.alert(
        'Password Reset',
        'Password reset instructions have been sent to your email.',
        [{ text: 'OK' }]
      );
    } catch (error) {
      Alert.alert(
        'Reset Error',
        'Failed to send password reset email. Please try again.',
        [{ text: 'OK' }]
      );
    }
  };

  const toggleMode = () => {
    setMode(mode === 'signin' ? 'signup' : 'signin');
    setErrors({});
  };

  return (
    <SafeAreaView className="flex-1 bg-background">
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        className="flex-1"
      >
        <Box className="flex-1 px-6 py-8">
          <VStack space="xl" className="flex-1 justify-center">
            {/* Header */}
            <VStack space="md" className="items-center">
              <Text className="text-3xl font-bold text-typography-900">
                {mode === 'signin' ? 'Welcome Back' : 'Create Account'}
              </Text>
              <Text className="text-lg text-typography-600 text-center">
                {mode === 'signin' 
                  ? 'Sign in to continue your learning journey'
                  : 'Join LearniScan to start learning smarter'
                }
              </Text>
            </VStack>

            {/* Email/Password Form */}
            <VStack space="lg">
              {mode === 'signup' && (
                <VStack space="sm">
                  <Input
                    variant="outline"
                    size="lg"
                    isInvalid={!!errors.name}
                  >
                    <InputField
                      placeholder="Full Name"
                      value={name}
                      onChangeText={setName}
                      autoCapitalize="words"
                      autoComplete="name"
                    />
                  </Input>
                  {errors.name && (
                    <Text className="text-sm text-error-600">{errors.name}</Text>
                  )}
                </VStack>
              )}

              <VStack space="sm">
                <Input
                  variant="outline"
                  size="lg"
                  isInvalid={!!errors.email}
                >
                  <InputField
                    placeholder="Email"
                    value={email}
                    onChangeText={setEmail}
                    keyboardType="email-address"
                    autoCapitalize="none"
                    autoComplete="email"
                  />
                </Input>
                {errors.email && (
                  <Text className="text-sm text-error-600">{errors.email}</Text>
                )}
              </VStack>

              <VStack space="sm">
                <Input
                  variant="outline"
                  size="lg"
                  isInvalid={!!errors.password}
                >
                  <InputField
                    placeholder="Password"
                    value={password}
                    onChangeText={setPassword}
                    secureTextEntry
                    autoComplete={mode === 'signup' ? 'new-password' : 'current-password'}
                  />
                </Input>
                {errors.password && (
                  <Text className="text-sm text-error-600">{errors.password}</Text>
                )}
              </VStack>

              {mode === 'signup' && (
                <VStack space="sm">
                  <Input
                    variant="outline"
                    size="lg"
                    isInvalid={!!errors.confirmPassword}
                  >
                    <InputField
                      placeholder="Confirm Password"
                      value={confirmPassword}
                      onChangeText={setConfirmPassword}
                      secureTextEntry
                      autoComplete="new-password"
                    />
                  </Input>
                  {errors.confirmPassword && (
                    <Text className="text-sm text-error-600">{errors.confirmPassword}</Text>
                  )}
                </VStack>
              )}

              {/* Sign In/Up Button */}
              <Button
                size="lg"
                onPress={handleEmailAuth}
                isDisabled={loading || !!oauthLoading}
                className="mt-4"
              >
                {loading ? (
                  <HStack space="sm" className="items-center">
                    <Spinner size="small" color="white" />
                    <ButtonText>
                      {mode === 'signin' ? 'Signing In...' : 'Creating Account...'}
                    </ButtonText>
                  </HStack>
                ) : (
                  <ButtonText>
                    {mode === 'signin' ? 'Sign In' : 'Create Account'}
                  </ButtonText>
                )}
              </Button>

              {/* Forgot Password */}
              {mode === 'signin' && (
                <Pressable onPress={handleForgotPassword}>
                  <Text className="text-center text-primary-600 font-medium">
                    Forgot Password?
                  </Text>
                </Pressable>
              )}
            </VStack>

            {/* Divider */}
            <HStack className="items-center" space="md">
              <Box className="flex-1 h-px bg-border-300" />
              <Text className="text-typography-500">or</Text>
              <Box className="flex-1 h-px bg-border-300" />
            </HStack>

            {/* OAuth Buttons */}
            <VStack space="md">
              {/* Auth0 OAuth */}
              <Button
                variant="outline"
                size="lg"
                onPress={() => handleOAuthSignIn('auth0', 'auth0')}
                isDisabled={loading || !!oauthLoading}
              >
                {oauthLoading === 'auth0' ? (
                  <HStack space="sm" className="items-center">
                    <Spinner size="small" />
                    <ButtonText>Connecting...</ButtonText>
                  </HStack>
                ) : (
                  <HStack space="sm" className="items-center">
                    <FontAwesome6 name="shield-halved" size={20} color="#EB5424" />
                    <ButtonText>Continue with Auth0</ButtonText>
                  </HStack>
                )}
              </Button>

              {/* Apple OAuth (iOS only) */}
              {Platform.OS === 'ios' && (
                <Button
                  variant="outline"
                  size="lg"
                  onPress={() => handleOAuthSignIn(OAuthProvider.Apple, 'apple')}
                  isDisabled={loading || !!oauthLoading}
                >
                  {oauthLoading === 'apple' ? (
                    <HStack space="sm" className="items-center">
                      <Spinner size="small" />
                      <ButtonText>Connecting...</ButtonText>
                    </HStack>
                  ) : (
                    <HStack space="sm" className="items-center">
                      <FontAwesome6 name="apple" size={20} color="#000" />
                      <ButtonText>Continue with Apple</ButtonText>
                    </HStack>
                  )}
                </Button>
              )}

              {/* Anonymous Sign In */}
              <Button
                variant="outline"
                size="lg"
                onPress={handleAnonymousSignIn}
                isDisabled={loading || !!oauthLoading}
              >
                <HStack space="sm" className="items-center">
                  <FontAwesome6 name="user-secret" size={20} color="#666" />
                  <ButtonText>Continue as Guest</ButtonText>
                </HStack>
              </Button>
            </VStack>

            {/* Toggle Mode */}
            <Pressable onPress={toggleMode}>
              <Text className="text-center text-typography-600">
                {mode === 'signin' 
                  ? "Don't have an account? " 
                  : "Already have an account? "
                }
                <Text className="text-primary-600 font-medium">
                  {mode === 'signin' ? 'Sign Up' : 'Sign In'}
                </Text>
              </Text>
            </Pressable>
          </VStack>
        </Box>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default AppwriteAuthScreen;
