import React from 'react';
import { Box } from '@/components/ui/box';
import { Text } from '@/components/ui/text';
import { Pressable } from '@/components/ui/pressable';
import { Camera, Upload, Image } from 'lucide-react-native';
import { cn } from '@/lib/utils';
import Animated, { 
  useSharedValue, 
  useAnimatedStyle, 
  withSpring,
  interpolateColor
} from 'react-native-reanimated';
import { useEffect } from 'react';

export type TabType = 'camera' | 'upload' | 'gallery';

export interface TabSelectorProps {
  activeTab: TabType;
  onTabChange: (tab: TabType) => void;
  className?: string;
}

interface TabItem {
  id: TabType;
  label: string;
  icon: React.ComponentType<any>;
  color: string; // Candy color for active state
}

const tabs: TabItem[] = [
  {
    id: 'camera',
    label: 'Camera',
    icon: Camera,
    color: '#FF6B9D', // Candy pink
  },
  {
    id: 'upload', 
    label: 'Upload',
    icon: Upload,
    color: '#3B82F6', // Candy blue
  },
  {
    id: 'gallery',
    label: 'Gallery', 
    icon: Image,
    color: '#A855F7', // Candy purple
  },
];

/**
 * TabSelector - Floating pill-shaped tab navigation with candy colors
 * 
 * Features:
 * - Floating glass morphism container
 * - Smooth animations with React Native Reanimated
 * - Candy color active states (pink, blue, purple)
 * - Icons with labels
 * - Spring-based transitions
 */
export const TabSelector = React.forwardRef<
  React.ElementRef<typeof Box>,
  TabSelectorProps
>(({ activeTab, onTabChange, className, ...props }, ref) => {

  // Animation values for each tab
  const cameraScale = useSharedValue(activeTab === 'camera' ? 1.05 : 1);
  const uploadScale = useSharedValue(activeTab === 'upload' ? 1.05 : 1);
  const galleryScale = useSharedValue(activeTab === 'gallery' ? 1.05 : 1);

  const cameraColor = useSharedValue(activeTab === 'camera' ? 1 : 0);
  const uploadColor = useSharedValue(activeTab === 'upload' ? 1 : 0);
  const galleryColor = useSharedValue(activeTab === 'gallery' ? 1 : 0);

  // Update animation values when active tab changes
  useEffect(() => {
    // Scale animations
    cameraScale.value = withSpring(activeTab === 'camera' ? 1.05 : 1);
    uploadScale.value = withSpring(activeTab === 'upload' ? 1.05 : 1);
    galleryScale.value = withSpring(activeTab === 'gallery' ? 1.05 : 1);

    // Color animations
    cameraColor.value = withSpring(activeTab === 'camera' ? 1 : 0);
    uploadColor.value = withSpring(activeTab === 'upload' ? 1 : 0);
    galleryColor.value = withSpring(activeTab === 'gallery' ? 1 : 0);
  }, [activeTab]);

  // Animated styles for each tab
  const cameraAnimatedStyle = useAnimatedStyle(() => {
    const backgroundColor = interpolateColor(
      cameraColor.value,
      [0, 1],
      ['transparent', '#FF6B9D']
    );
    return {
      transform: [{ scale: cameraScale.value }],
      backgroundColor,
    };
  });

  const uploadAnimatedStyle = useAnimatedStyle(() => {
    const backgroundColor = interpolateColor(
      uploadColor.value,
      [0, 1],
      ['transparent', '#3B82F6']
    );
    return {
      transform: [{ scale: uploadScale.value }],
      backgroundColor,
    };
  });

  const galleryAnimatedStyle = useAnimatedStyle(() => {
    const backgroundColor = interpolateColor(
      galleryColor.value,
      [0, 1],
      ['transparent', '#A855F7']
    );
    return {
      transform: [{ scale: galleryScale.value }],
      backgroundColor,
    };
  });

  return (
    <Box
      ref={ref}
      className={cn(
        // Floating container positioning  
        'absolute top-32 left-5 right-5 z-40',
        // Glass morphism styling
        'glass-card rounded-full px-4 py-3',
        // Layout
        'flex-row items-center justify-around',
        className
      )}
      {...props}
    >
      {/* Camera Tab */}
      <Animated.View style={[cameraAnimatedStyle]} className="rounded-full px-4 py-2">
        <Pressable
          onPress={() => onTabChange('camera')}
          className="flex-col items-center gap-1"
        >
          <Camera
            size={20}
            color={activeTab === 'camera' ? '#FFFFFF' : 'rgba(255, 255, 255, 0.7)'}
          />
          <Text 
            className={cn(
              'text-xs font-medium',
              activeTab === 'camera' ? 'text-white' : 'text-white/70'
            )}
          >
            Camera
          </Text>
        </Pressable>
      </Animated.View>

      {/* Upload Tab */}
      <Animated.View style={[uploadAnimatedStyle]} className="rounded-full px-4 py-2">
        <Pressable
          onPress={() => onTabChange('upload')}
          className="flex-col items-center gap-1"
        >
          <Upload
            size={20}
            color={activeTab === 'upload' ? '#FFFFFF' : 'rgba(255, 255, 255, 0.7)'}
          />
          <Text 
            className={cn(
              'text-xs font-medium',
              activeTab === 'upload' ? 'text-white' : 'text-white/70'
            )}
          >
            Upload
          </Text>
        </Pressable>
      </Animated.View>

      {/* Gallery Tab */}
      <Animated.View style={[galleryAnimatedStyle]} className="rounded-full px-4 py-2">
        <Pressable
          onPress={() => onTabChange('gallery')}
          className="flex-col items-center gap-1"
        >
          <Image
            size={20}
            color={activeTab === 'gallery' ? '#FFFFFF' : 'rgba(255, 255, 255, 0.7)'}
          />
          <Text 
            className={cn(
              'text-xs font-medium',
              activeTab === 'gallery' ? 'text-white' : 'text-white/70'
            )}
          >
            Gallery
          </Text>
        </Pressable>
      </Animated.View>
    </Box>
  );
});

TabSelector.displayName = 'TabSelector';

export default TabSelector;
