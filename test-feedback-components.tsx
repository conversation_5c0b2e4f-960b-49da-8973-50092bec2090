import React from 'react';
import { View } from 'react-native';
import { Spinner } from './components/ui/spinner';
import { Toast, ToastTitle, ToastDescription, useToast } from './components/ui/toast';
import { Text } from './components/ui/text';
import { Button, ButtonText } from './components/ui/button';
import { VStack } from './components/ui/vstack';
import { HStack } from './components/ui/hstack';

// Test component to validate enhanced Feedback Components
export function LearniScanFeedbackTest() {
  const toast = useToast();

  const showCandyToast = (action: string, variant: string) => {
    toast.show({
      id: `${action}-${variant}-${Date.now()}`,
      placement: 'top',
      duration: 3000,
      render: ({ id }) => (
        <Toast nativeID={`toast-${id}`} action={action} variant={variant}>
          <VStack space="xs">
            <ToastTitle>LearniScan {action} Toast</ToastTitle>
            <ToastDescription>
              This is a {variant} variant with {action} styling
            </ToastDescription>
          </VStack>
        </Toast>
      ),
    });
  };

  return (
    <View style={{ padding: 20, backgroundColor: '#000' }}>
      <Text color="primary" style={{ marginBottom: 20, fontSize: 18 }}>
        Enhanced Feedback Components Test
      </Text>

      {/* Spinner Candy Color Variants */}
      <Text color="secondary" style={{ marginBottom: 10 }}>Spinner Candy Color Variants:</Text>
      
      <HStack space="lg" style={{ marginBottom: 20 }}>
        <VStack space="xs" style={{ alignItems: 'center' }}>
          <Spinner color="candyPink" size="large" />
          <Text color="candyPink" size="xs">Candy Pink</Text>
        </VStack>
        
        <VStack space="xs" style={{ alignItems: 'center' }}>
          <Spinner color="candyPurple" size="large" />
          <Text color="candyPurple" size="xs">Candy Purple</Text>
        </VStack>
        
        <VStack space="xs" style={{ alignItems: 'center' }}>
          <Spinner color="candyBlue" size="large" />
          <Text color="candyBlue" size="xs">Candy Blue</Text>
        </VStack>
        
        <VStack space="xs" style={{ alignItems: 'center' }}>
          <Spinner color="white" size="large" />
          <Text color="primary" size="xs">White</Text>
        </VStack>
      </HStack>

      {/* Spinner Size Variants */}
      <Text color="secondary" style={{ marginBottom: 10 }}>Spinner Size Variants:</Text>
      
      <HStack space="lg" style={{ marginBottom: 20, alignItems: 'center' }}>
        <VStack space="xs" style={{ alignItems: 'center' }}>
          <Spinner color="candyPink" size="small" />
          <Text color="tertiary" size="xs">Small</Text>
        </VStack>
        
        <VStack space="xs" style={{ alignItems: 'center' }}>
          <Spinner color="candyPink" size="medium" />
          <Text color="tertiary" size="xs">Medium</Text>
        </VStack>
        
        <VStack space="xs" style={{ alignItems: 'center' }}>
          <Spinner color="candyPink" size="large" />
          <Text color="tertiary" size="xs">Large</Text>
        </VStack>
        
        <VStack space="xs" style={{ alignItems: 'center' }}>
          <Spinner color="candyPink" size="xl" />
          <Text color="tertiary" size="xs">XL</Text>
        </VStack>
      </HStack>

      {/* Toast Candy Color Variants */}
      <Text color="secondary" style={{ marginBottom: 10 }}>Toast Candy Color Variants:</Text>
      
      <VStack space="sm" style={{ marginBottom: 20 }}>
        <HStack space="sm">
          <Button 
            action="candyPink" 
            size="sm"
            onPress={() => showCandyToast('candyPink', 'solid')}
          >
            <ButtonText>Pink Solid Toast</ButtonText>
          </Button>
          
          <Button 
            action="candyPurple" 
            size="sm"
            onPress={() => showCandyToast('candyPurple', 'solid')}
          >
            <ButtonText>Purple Solid Toast</ButtonText>
          </Button>
        </HStack>
        
        <HStack space="sm">
          <Button 
            action="candyBlue" 
            size="sm"
            onPress={() => showCandyToast('candyBlue', 'solid')}
          >
            <ButtonText>Blue Solid Toast</ButtonText>
          </Button>
          
          <Button 
            action="glass" 
            size="sm"
            onPress={() => showCandyToast('candyPink', 'outline')}
          >
            <ButtonText>Pink Outline Toast</ButtonText>
          </Button>
        </HStack>
      </VStack>

      {/* Toast Glass Effect Variants */}
      <Text color="secondary" style={{ marginBottom: 10 }}>Toast Glass Effect Variants:</Text>
      
      <VStack space="sm" style={{ marginBottom: 20 }}>
        <HStack space="sm">
          <Button 
            action="glass" 
            size="sm"
            onPress={() => showCandyToast('candyPink', 'glass')}
          >
            <ButtonText>Glass Toast</ButtonText>
          </Button>
          
          <Button 
            action="glassCard" 
            size="sm"
            onPress={() => showCandyToast('candyPurple', 'glassCard')}
          >
            <ButtonText>Glass Card Toast</ButtonText>
          </Button>
        </HStack>
      </VStack>

      {/* Loading States with Spinners */}
      <Text color="secondary" style={{ marginBottom: 10 }}>Loading States:</Text>
      
      <VStack space="md">
        <HStack space="md" style={{ alignItems: 'center' }}>
          <Spinner color="candyPink" size="medium" />
          <Text color="primary">Loading LearniScan data...</Text>
        </HStack>
        
        <HStack space="md" style={{ alignItems: 'center' }}>
          <Spinner color="candyPurple" size="medium" />
          <Text color="primary">Processing document scan...</Text>
        </HStack>
        
        <HStack space="md" style={{ alignItems: 'center' }}>
          <Spinner color="candyBlue" size="medium" />
          <Text color="primary">Analyzing knowledge graph...</Text>
        </HStack>
      </VStack>
    </View>
  );
}