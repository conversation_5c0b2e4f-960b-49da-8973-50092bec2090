import React, { useState } from 'react';
import { View } from 'react-native';
import { 
  Accordion, 
  AccordionItem, 
  AccordionHeader, 
  AccordionTrigger, 
  AccordionTitleText, 
  AccordionIcon, 
  AccordionContent, 
  AccordionContentText 
} from './components/ui/accordion';
import { 
  Select, 
  SelectTrigger, 
  SelectInput, 
  SelectIcon, 
  SelectPortal, 
  SelectBackdrop, 
  SelectContent, 
  SelectDragIndicatorWrapper, 
  SelectDragIndicator, 
  SelectItem 
} from './components/ui/select';
import { Text } from './components/ui/text';
import { VStack } from './components/ui/vstack';
import { HStack } from './components/ui/hstack';
import { ChevronDownIcon, ChevronUpIcon, SettingsIcon, UserIcon, BookOpenIcon, HelpCircleIcon } from 'lucide-react-native';

// Test component to validate enhanced Navigation Components
export function LearniScanNavigationTest() {
  const [selectedLanguage, setSelectedLanguage] = useState('');
  const [selectedQuality, setSelectedQuality] = useState('');
  const [selectedTheme, setSelectedTheme] = useState('');

  return (
    <View style={{ padding: 20, backgroundColor: '#000' }}>
      <Text color="primary" style={{ marginBottom: 20, fontSize: 18 }}>
        Enhanced Navigation Components Test
      </Text>

      {/* Accordion Glass Variants */}
      <Text color="secondary" style={{ marginBottom: 15, fontSize: 16 }}>
        Accordion Glass Variants:
      </Text>
      
      <VStack space="lg" style={{ marginBottom: 25 }}>
        {/* Glass Accordion */}
        <Accordion variant="glass" size="md">
          <AccordionItem>
            <AccordionHeader>
              <AccordionTrigger>
                {({ isExpanded }) => (
                  <>
                    <AccordionTitleText>LearniScan Settings</AccordionTitleText>
                    <AccordionIcon as={isExpanded ? ChevronUpIcon : ChevronDownIcon} />
                  </>
                )}
              </AccordionTrigger>
            </AccordionHeader>
            <AccordionContent>
              <AccordionContentText>
                Configure your LearniScan preferences including scan quality, language settings, 
                and AI processing options. These settings will apply to all your document scans.
              </AccordionContentText>
            </AccordionContent>
          </AccordionItem>

          <AccordionItem>
            <AccordionHeader>
              <AccordionTrigger>
                {({ isExpanded }) => (
                  <>
                    <AccordionTitleText>Account & Subscription</AccordionTitleText>
                    <AccordionIcon as={isExpanded ? ChevronUpIcon : ChevronDownIcon} />
                  </>
                )}
              </AccordionTrigger>
            </AccordionHeader>
            <AccordionContent>
              <AccordionContentText>
                Manage your LearniScan account, view subscription details, and upgrade to premium 
                features for enhanced AI processing and unlimited cloud storage.
              </AccordionContentText>
            </AccordionContent>
          </AccordionItem>
        </Accordion>

        {/* Glass Card Accordion */}
        <Accordion variant="glassCard" size="lg">
          <AccordionItem>
            <AccordionHeader>
              <AccordionTrigger>
                {({ isExpanded }) => (
                  <>
                    <AccordionTitleText>Help & Support</AccordionTitleText>
                    <AccordionIcon as={isExpanded ? ChevronUpIcon : ChevronDownIcon} />
                  </>
                )}
              </AccordionTrigger>
            </AccordionHeader>
            <AccordionContent>
              <AccordionContentText>
                Get help with LearniScan features, troubleshoot scanning issues, and access 
                our comprehensive knowledge base for tips and best practices.
              </AccordionContentText>
            </AccordionContent>
          </AccordionItem>
        </Accordion>

        {/* Traditional Filled Accordion */}
        <Accordion variant="filled" size="md">
          <AccordionItem>
            <AccordionHeader>
              <AccordionTrigger>
                {({ isExpanded }) => (
                  <>
                    <AccordionTitleText>Privacy & Security</AccordionTitleText>
                    <AccordionIcon as={isExpanded ? ChevronUpIcon : ChevronDownIcon} />
                  </>
                )}
              </AccordionTrigger>
            </AccordionHeader>
            <AccordionContent>
              <AccordionContentText>
                Review privacy settings, manage data sharing preferences, and configure 
                security options for your scanned documents and personal information.
              </AccordionContentText>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </VStack>

      {/* Select Glass Variants */}
      <Text color="secondary" style={{ marginBottom: 15, fontSize: 16 }}>
        Select Glass Variants:
      </Text>
      
      <VStack space="lg" style={{ marginBottom: 25 }}>
        {/* Glass Select */}
        <VStack space="xs">
          <Text color="tertiary" size="sm">Scan Language (Glass):</Text>
          <Select selectedValue={selectedLanguage} onValueChange={setSelectedLanguage}>
            <SelectTrigger variant="glass" size="md">
              <SelectInput placeholder="Select language..." />
              <SelectIcon as={ChevronDownIcon} />
            </SelectTrigger>
            <SelectPortal>
              <SelectBackdrop />
              <SelectContent>
                <SelectDragIndicatorWrapper>
                  <SelectDragIndicator />
                </SelectDragIndicatorWrapper>
                <SelectItem label="English" value="en" />
                <SelectItem label="Spanish" value="es" />
                <SelectItem label="French" value="fr" />
                <SelectItem label="German" value="de" />
                <SelectItem label="Chinese" value="zh" />
                <SelectItem label="Japanese" value="ja" />
              </SelectContent>
            </SelectPortal>
          </Select>
        </VStack>

        {/* Glass Rounded Select */}
        <VStack space="xs">
          <Text color="tertiary" size="sm">Scan Quality (Glass Rounded):</Text>
          <Select selectedValue={selectedQuality} onValueChange={setSelectedQuality}>
            <SelectTrigger variant="glassRounded" size="lg">
              <SelectInput placeholder="Select quality..." />
              <SelectIcon as={ChevronDownIcon} />
            </SelectTrigger>
            <SelectPortal>
              <SelectBackdrop />
              <SelectContent>
                <SelectDragIndicatorWrapper>
                  <SelectDragIndicator />
                </SelectDragIndicatorWrapper>
                <SelectItem label="Ultra HD (Premium)" value="ultra" />
                <SelectItem label="High Quality" value="high" />
                <SelectItem label="Medium Quality" value="medium" />
                <SelectItem label="Fast Scan" value="fast" />
              </SelectContent>
            </SelectPortal>
          </Select>
        </VStack>

        {/* Traditional Outline Select */}
        <VStack space="xs">
          <Text color="tertiary" size="sm">App Theme (Outline):</Text>
          <Select selectedValue={selectedTheme} onValueChange={setSelectedTheme}>
            <SelectTrigger variant="outline" size="md">
              <SelectInput placeholder="Select theme..." />
              <SelectIcon as={ChevronDownIcon} />
            </SelectTrigger>
            <SelectPortal>
              <SelectBackdrop />
              <SelectContent>
                <SelectDragIndicatorWrapper>
                  <SelectDragIndicator />
                </SelectDragIndicatorWrapper>
                <SelectItem label="Dark Mode" value="dark" />
                <SelectItem label="Light Mode" value="light" />
                <SelectItem label="Auto (System)" value="auto" />
              </SelectContent>
            </SelectPortal>
          </Select>
        </VStack>

        {/* Rounded Select */}
        <VStack space="xs">
          <Text color="tertiary" size="sm">Export Format (Rounded):</Text>
          <Select>
            <SelectTrigger variant="rounded" size="sm">
              <SelectInput placeholder="Select format..." />
              <SelectIcon as={ChevronDownIcon} />
            </SelectTrigger>
            <SelectPortal>
              <SelectBackdrop />
              <SelectContent>
                <SelectDragIndicatorWrapper>
                  <SelectDragIndicator />
                </SelectDragIndicatorWrapper>
                <SelectItem label="PDF Document" value="pdf" />
                <SelectItem label="JPEG Image" value="jpg" />
                <SelectItem label="PNG Image" value="png" />
                <SelectItem label="Text File" value="txt" />
              </SelectContent>
            </SelectPortal>
          </Select>
        </VStack>
      </VStack>

      {/* Real-World LearniScan Usage */}
      <Text color="secondary" style={{ marginBottom: 15, fontSize: 16 }}>
        Real-World LearniScan Usage:
      </Text>
      
      <VStack space="lg">
        {/* Settings Panel with Glass Accordion */}
        <VStack space="sm">
          <Text color="tertiary" size="sm">Settings Panel:</Text>
          
          <Accordion variant="glass" size="md">
            <AccordionItem>
              <AccordionHeader>
                <AccordionTrigger>
                  {({ isExpanded }) => (
                    <>
                      <HStack space="sm" style={{ alignItems: 'center', flex: 1 }}>
                        <SettingsIcon size={18} color="#FF6B9D" />
                        <AccordionTitleText>Scan Preferences</AccordionTitleText>
                      </HStack>
                      <AccordionIcon as={isExpanded ? ChevronUpIcon : ChevronDownIcon} />
                    </>
                  )}
                </AccordionTrigger>
              </AccordionHeader>
              <AccordionContent>
                <VStack space="md">
                  <Select>
                    <SelectTrigger variant="glass" size="sm">
                      <SelectInput placeholder="Default quality..." />
                      <SelectIcon as={ChevronDownIcon} />
                    </SelectTrigger>
                  </Select>
                  <Select>
                    <SelectTrigger variant="glass" size="sm">
                      <SelectInput placeholder="Auto-save location..." />
                      <SelectIcon as={ChevronDownIcon} />
                    </SelectTrigger>
                  </Select>
                </VStack>
              </AccordionContent>
            </AccordionItem>

            <AccordionItem>
              <AccordionHeader>
                <AccordionTrigger>
                  {({ isExpanded }) => (
                    <>
                      <HStack space="sm" style={{ alignItems: 'center', flex: 1 }}>
                        <UserIcon size={18} color="#A855F7" />
                        <AccordionTitleText>Account Settings</AccordionTitleText>
                      </HStack>
                      <AccordionIcon as={isExpanded ? ChevronUpIcon : ChevronDownIcon} />
                    </>
                  )}
                </AccordionTrigger>
              </AccordionHeader>
              <AccordionContent>
                <AccordionContentText>
                  Manage your profile, subscription, and data sync preferences.
                </AccordionContentText>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </VStack>

        {/* Quick Actions with Glass Selects */}
        <VStack space="sm">
          <Text color="tertiary" size="sm">Quick Actions:</Text>
          
          <HStack space="md">
            <VStack space="xs" style={{ flex: 1 }}>
              <Select>
                <SelectTrigger variant="glassRounded" size="sm">
                  <SelectInput placeholder="Language" />
                  <SelectIcon as={ChevronDownIcon} />
                </SelectTrigger>
              </Select>
            </VStack>
            
            <VStack space="xs" style={{ flex: 1 }}>
              <Select>
                <SelectTrigger variant="glass" size="sm">
                  <SelectInput placeholder="Quality" />
                  <SelectIcon as={ChevronDownIcon} />
                </SelectTrigger>
              </Select>
            </VStack>
          </HStack>
        </VStack>
      </VStack>
    </View>
  );
}