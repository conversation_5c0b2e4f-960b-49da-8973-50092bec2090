import React, { useState } from 'react';
import { View } from 'react-native';
import { Checkbox, CheckboxIndicator, CheckboxIcon, CheckboxLabel } from './components/ui/checkbox';
import { Radio, RadioGroup, RadioIndicator, RadioIcon, RadioLabel } from './components/ui/radio';
import { Switch } from './components/ui/switch';
import { Text } from './components/ui/text';
import { VStack } from './components/ui/vstack';
import { HStack } from './components/ui/hstack';
import { CheckIcon } from 'lucide-react-native';

// Test component to validate enhanced Form Components
export function LearniScanFormTest() {
  const [checkboxValues, setCheckboxValues] = useState({
    option1: false,
    option2: true,
    option3: false,
  });

  const [radioValue, setRadioValue] = useState('option1');
  const [switchValues, setSwitchValues] = useState({
    notifications: true,
    darkMode: false,
    autoScan: true,
  });

  return (
    <View style={{ padding: 20, backgroundColor: '#000' }}>
      <Text color="primary" style={{ marginBottom: 20, fontSize: 18 }}>
        Enhanced Form Components Test
      </Text>

      {/* Checkbox Components */}
      <Text color="secondary" style={{ marginBottom: 15, fontSize: 16 }}>
        Checkbox Components (Candy Pink Accents):
      </Text>
      
      <VStack space="md" style={{ marginBottom: 25 }}>
        <Checkbox
          size="lg"
          isChecked={checkboxValues.option1}
          onChange={(isChecked) => 
            setCheckboxValues(prev => ({ ...prev, option1: isChecked }))
          }
        >
          <CheckboxIndicator>
            <CheckboxIcon as={CheckIcon} />
          </CheckboxIndicator>
          <CheckboxLabel>Enable document auto-scanning</CheckboxLabel>
        </Checkbox>

        <Checkbox
          size="md"
          isChecked={checkboxValues.option2}
          onChange={(isChecked) => 
            setCheckboxValues(prev => ({ ...prev, option2: isChecked }))
          }
        >
          <CheckboxIndicator>
            <CheckboxIcon as={CheckIcon} />
          </CheckboxIndicator>
          <CheckboxLabel>Save scanned documents to cloud</CheckboxLabel>
        </Checkbox>

        <Checkbox
          size="sm"
          isChecked={checkboxValues.option3}
          onChange={(isChecked) => 
            setCheckboxValues(prev => ({ ...prev, option3: isChecked }))
          }
        >
          <CheckboxIndicator>
            <CheckboxIcon as={CheckIcon} />
          </CheckboxIndicator>
          <CheckboxLabel>Enable OCR text recognition</CheckboxLabel>
        </Checkbox>

        {/* Disabled Checkbox */}
        <Checkbox size="md" isDisabled isChecked={true}>
          <CheckboxIndicator>
            <CheckboxIcon as={CheckIcon} />
          </CheckboxIndicator>
          <CheckboxLabel>Premium features (disabled)</CheckboxLabel>
        </Checkbox>
      </VStack>

      {/* Radio Components */}
      <Text color="secondary" style={{ marginBottom: 15, fontSize: 16 }}>
        Radio Components (Candy Pink Selection):
      </Text>
      
      <RadioGroup 
        value={radioValue} 
        onChange={setRadioValue}
        style={{ marginBottom: 25 }}
      >
        <VStack space="md">
          <Radio value="option1" size="lg">
            <RadioIndicator>
              <RadioIcon />
            </RadioIndicator>
            <RadioLabel>High quality scan (Recommended)</RadioLabel>
          </Radio>

          <Radio value="option2" size="md">
            <RadioIndicator>
              <RadioIcon />
            </RadioIndicator>
            <RadioLabel>Medium quality scan</RadioLabel>
          </Radio>

          <Radio value="option3" size="sm">
            <RadioIndicator>
              <RadioIcon />
            </RadioIndicator>
            <RadioLabel>Fast scan (lower quality)</RadioLabel>
          </Radio>

          {/* Disabled Radio */}
          <Radio value="option4" size="md" isDisabled>
            <RadioIndicator>
              <RadioIcon />
            </RadioIndicator>
            <RadioLabel>Ultra HD scan (Premium only)</RadioLabel>
          </Radio>
        </VStack>
      </RadioGroup>

      {/* Switch Components */}
      <Text color="secondary" style={{ marginBottom: 15, fontSize: 16 }}>
        Switch Components (Candy Pink Active State):
      </Text>
      
      <VStack space="lg" style={{ marginBottom: 25 }}>
        <HStack space="md" style={{ alignItems: 'center', justifyContent: 'space-between' }}>
          <VStack space="xs" style={{ flex: 1 }}>
            <Text color="primary">Push Notifications</Text>
            <Text color="tertiary" size="sm">Get notified when scans complete</Text>
          </VStack>
          <Switch
            size="lg"
            value={switchValues.notifications}
            onValueChange={(value) => 
              setSwitchValues(prev => ({ ...prev, notifications: value }))
            }
            trackColor={{ false: '#767577', true: '#FF6B9D' }}
            thumbColor={switchValues.notifications ? '#FFFFFF' : '#f4f3f4'}
          />
        </HStack>

        <HStack space="md" style={{ alignItems: 'center', justifyContent: 'space-between' }}>
          <VStack space="xs" style={{ flex: 1 }}>
            <Text color="primary">Dark Mode</Text>
            <Text color="tertiary" size="sm">Use dark theme for better battery life</Text>
          </VStack>
          <Switch
            size="md"
            value={switchValues.darkMode}
            onValueChange={(value) => 
              setSwitchValues(prev => ({ ...prev, darkMode: value }))
            }
            trackColor={{ false: '#767577', true: '#FF6B9D' }}
            thumbColor={switchValues.darkMode ? '#FFFFFF' : '#f4f3f4'}
          />
        </HStack>

        <HStack space="md" style={{ alignItems: 'center', justifyContent: 'space-between' }}>
          <VStack space="xs" style={{ flex: 1 }}>
            <Text color="primary">Auto-Scan Documents</Text>
            <Text color="tertiary" size="sm">Automatically scan when camera detects documents</Text>
          </VStack>
          <Switch
            size="sm"
            value={switchValues.autoScan}
            onValueChange={(value) => 
              setSwitchValues(prev => ({ ...prev, autoScan: value }))
            }
            trackColor={{ false: '#767577', true: '#FF6B9D' }}
            thumbColor={switchValues.autoScan ? '#FFFFFF' : '#f4f3f4'}
          />
        </HStack>

        {/* Disabled Switch */}
        <HStack space="md" style={{ alignItems: 'center', justifyContent: 'space-between' }}>
          <VStack space="xs" style={{ flex: 1 }}>
            <Text color="muted">Cloud Sync (Premium)</Text>
            <Text color="muted" size="sm">Sync documents across all devices</Text>
          </VStack>
          <Switch
            size="md"
            value={false}
            disabled
            trackColor={{ false: '#767577', true: '#FF6B9D' }}
            thumbColor={'#f4f3f4'}
          />
        </HStack>
      </VStack>

      {/* Form State Summary */}
      <Text color="secondary" style={{ marginBottom: 10, fontSize: 16 }}>
        Current Form State:
      </Text>
      
      <VStack space="xs" style={{ backgroundColor: 'rgba(255, 255, 255, 0.05)', padding: 16, borderRadius: 12 }}>
        <Text color="tertiary" size="sm">
          Checkboxes: {Object.entries(checkboxValues).filter(([_, value]) => value).map(([key]) => key).join(', ') || 'None selected'}
        </Text>
        <Text color="tertiary" size="sm">
          Radio: {radioValue}
        </Text>
        <Text color="tertiary" size="sm">
          Switches: {Object.entries(switchValues).filter(([_, value]) => value).map(([key]) => key).join(', ') || 'None enabled'}
        </Text>
      </VStack>
    </View>
  );
}