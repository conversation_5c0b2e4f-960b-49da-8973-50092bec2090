#!/usr/bin/env tsx

/**
 * Simple AppWrite Connection Test
 * Tests basic connectivity and configuration
 */

import { Client, Databases, Account } from 'node-appwrite';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

const config = {
  endpoint: process.env.EXPO_PUBLIC_APPWRITE_ENDPOINT || 'https://cloud.appwrite.io/v1',
  projectId: process.env.EXPO_PUBLIC_APPWRITE_PROJECT_ID!,
  apiKey: process.env.APPWRITE_API_KEY!,
  databaseId: process.env.EXPO_PUBLIC_APPWRITE_DATABASE_ID || 'learni-scan-db',
};

console.log('🧪 Testing AppWrite Connection...\n');

// Initialize client
const client = new Client()
  .setEndpoint(config.endpoint)
  .setProject(config.projectId)
  .setKey(config.apiKey);

const databases = new Databases(client);
const account = new Account(client);

async function testConnection() {
  try {
    console.log('📡 Testing basic connectivity...');
    
    // Test 1: List databases
    console.log('1. Testing database access...');
    const dbList = await databases.list();
    console.log(`   ✅ Found ${dbList.total} database(s)`);
    
    // Test 2: Get specific database
    console.log('2. Testing specific database...');
    const db = await databases.get(config.databaseId);
    console.log(`   ✅ Database "${db.name}" found (ID: ${db.$id})`);
    
    // Test 3: List collections
    console.log('3. Testing collections...');
    const collections = await databases.listCollections(config.databaseId);
    console.log(`   ✅ Found ${collections.total} collection(s):`);
    collections.collections.forEach(col => {
      console.log(`      - ${col.name} (${col.$id})`);
    });
    
    console.log('\n🎉 AppWrite connection test successful!');
    console.log('\n📊 Configuration Summary:');
    console.log(`   Endpoint: ${config.endpoint}`);
    console.log(`   Project ID: ${config.projectId}`);
    console.log(`   Database ID: ${config.databaseId}`);
    console.log(`   API Key: ${config.apiKey.substring(0, 20)}...`);
    
  } catch (error: any) {
    console.error('\n❌ AppWrite connection test failed:');
    console.error(`   Error: ${error.message}`);
    console.error(`   Code: ${error.code || 'Unknown'}`);
    
    if (error.code === 401) {
      console.error('\n💡 Troubleshooting:');
      console.error('   - Check if your API key is correct');
      console.error('   - Ensure the API key has the required permissions');
    } else if (error.code === 404) {
      console.error('\n💡 Troubleshooting:');
      console.error('   - Check if your project ID is correct');
      console.error('   - Ensure the database exists');
    }
    
    process.exit(1);
  }
}

// Run the test
testConnection();
