#!/usr/bin/env tsx

/**
 * AppWrite Integration Test Script for LearniScan
 * 
 * This script tests all AppWrite services to ensure proper configuration
 * and functionality before deploying to production.
 * 
 * Tests include:
 * - Authentication (email/password, anonymous)
 * - Database operations (CRUD for all collections)
 * - Storage operations (file upload/download)
 * - Permissions and security
 * 
 * Usage:
 * npm run test:appwrite
 * or
 * npx tsx scripts/test-appwrite-integration.ts
 */

import { appwriteService } from '../lib/services/appwrite.service';
import type { KnowledgeCard, LearningSession, ScanHistory } from '../types/appwrite';

// Test configuration
const TEST_CONFIG = {
  testEmail: '<EMAIL>',
  testPassword: 'TestPassword123!',
  testName: 'Test User',
  cleanup: true, // Set to false to keep test data for inspection
};

// Test results tracking
interface TestResult {
  name: string;
  passed: boolean;
  error?: string;
  duration: number;
}

const testResults: TestResult[] = [];

// Utility function to run tests
async function runTest(name: string, testFn: () => Promise<void>): Promise<void> {
  const startTime = Date.now();
  console.log(`🧪 Running: ${name}`);
  
  try {
    await testFn();
    const duration = Date.now() - startTime;
    testResults.push({ name, passed: true, duration });
    console.log(`✅ Passed: ${name} (${duration}ms)`);
  } catch (error: any) {
    const duration = Date.now() - startTime;
    testResults.push({ name, passed: false, error: error.message, duration });
    console.error(`❌ Failed: ${name} (${duration}ms):`, error.message);
  }
}

// Test data storage
let testUser: any = null;
let testUserProfile: any = null;
let testKnowledgeCard: KnowledgeCard | null = null;
let testLearningSession: LearningSession | null = null;
let testScanRecord: ScanHistory | null = null;

// Main test runner
async function runAppwriteTests() {
  console.log('🚀 Starting AppWrite Integration Tests for LearniScan\n');

  // Authentication Tests
  console.log('🔐 Authentication Tests');
  await runTest('Anonymous Session Creation', testAnonymousSession);
  await runTest('Email/Password Sign Up', testEmailSignUp);
  await runTest('Email/Password Sign In', testEmailSignIn);
  await runTest('User Profile Initialization', testUserProfileInit);

  // Database Tests
  console.log('\n📊 Database Tests');
  await runTest('Knowledge Card Creation', testKnowledgeCardCreation);
  await runTest('Knowledge Card Retrieval', testKnowledgeCardRetrieval);
  await runTest('Knowledge Card Update', testKnowledgeCardUpdate);
  await runTest('Learning Session Creation', testLearningSessionCreation);
  await runTest('Scan History Creation', testScanHistoryCreation);

  // Storage Tests
  console.log('\n💾 Storage Tests');
  await runTest('File Upload Simulation', testFileUpload);

  // Security Tests
  console.log('\n🛡️ Security Tests');
  await runTest('Permission Validation', testPermissions);

  // Cleanup
  if (TEST_CONFIG.cleanup) {
    console.log('\n🧹 Cleanup');
    await runTest('Test Data Cleanup', testCleanup);
  }

  // Results Summary
  printTestSummary();
}

// Authentication Tests
async function testAnonymousSession() {
  const user = await appwriteService.auth.createAnonymousSession();
  if (!user || !user.$id) {
    throw new Error('Anonymous session creation failed');
  }
  console.log(`   Anonymous user created: ${user.$id}`);
}

async function testEmailSignUp() {
  try {
    testUser = await appwriteService.auth.signUp(
      TEST_CONFIG.testEmail,
      TEST_CONFIG.testPassword,
      TEST_CONFIG.testName
    );
    
    if (!testUser || !testUser.$id) {
      throw new Error('User sign up failed');
    }
    
    console.log(`   User created: ${testUser.name} (${testUser.$id})`);
  } catch (error: any) {
    if (error.message.includes('already exists')) {
      console.log('   User already exists, attempting sign in...');
      testUser = await appwriteService.auth.signIn(TEST_CONFIG.testEmail, TEST_CONFIG.testPassword);
    } else {
      throw error;
    }
  }
}

async function testEmailSignIn() {
  const user = await appwriteService.auth.signIn(TEST_CONFIG.testEmail, TEST_CONFIG.testPassword);
  if (!user || user.$id !== testUser.$id) {
    throw new Error('User sign in failed or user mismatch');
  }
  console.log(`   User signed in: ${user.name}`);
}

async function testUserProfileInit() {
  testUserProfile = await appwriteService.initializeUser(testUser);
  if (!testUserProfile || !testUserProfile.$id) {
    throw new Error('User profile initialization failed');
  }
  console.log(`   User profile created: ${testUserProfile.$id}`);
}

// Database Tests
async function testKnowledgeCardCreation() {
  const cardData = {
    userId: testUser.$id,
    title: 'Test Knowledge Card',
    content: 'This is a test knowledge card created during integration testing.',
    sourceType: 'manual' as const,
    tags: ['test', 'integration'],
    difficulty: 'beginner' as const,
    category: 'Testing',
    reviewData: {
      nextReview: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
      interval: 1,
      easeFactor: 2.5,
      reviewCount: 0,
      correctCount: 0,
    },
    aiEnhanced: false,
    isPublic: false,
  };

  testKnowledgeCard = await appwriteService.database.createKnowledgeCard(cardData);
  
  if (!testKnowledgeCard || !testKnowledgeCard.$id) {
    throw new Error('Knowledge card creation failed');
  }
  
  console.log(`   Knowledge card created: ${testKnowledgeCard.title} (${testKnowledgeCard.$id})`);
}

async function testKnowledgeCardRetrieval() {
  if (!testKnowledgeCard) {
    throw new Error('No test knowledge card available');
  }

  const retrievedCard = await appwriteService.database.getKnowledgeCard(testKnowledgeCard.$id);
  
  if (!retrievedCard || retrievedCard.$id !== testKnowledgeCard.$id) {
    throw new Error('Knowledge card retrieval failed');
  }
  
  console.log(`   Knowledge card retrieved: ${retrievedCard.title}`);
}

async function testKnowledgeCardUpdate() {
  if (!testKnowledgeCard) {
    throw new Error('No test knowledge card available');
  }

  const updatedCard = await appwriteService.database.updateKnowledgeCard(
    testKnowledgeCard.$id,
    { title: 'Updated Test Knowledge Card' }
  );
  
  if (!updatedCard || updatedCard.title !== 'Updated Test Knowledge Card') {
    throw new Error('Knowledge card update failed');
  }
  
  console.log(`   Knowledge card updated: ${updatedCard.title}`);
  testKnowledgeCard = updatedCard;
}

async function testLearningSessionCreation() {
  const sessionData = {
    userId: testUser.$id,
    sessionType: 'review' as const,
    cardsReviewed: [testKnowledgeCard?.$id || 'test-card-id'],
    performance: {
      totalCards: 1,
      correctAnswers: 1,
      averageTime: 30,
      accuracy: 100,
    },
    duration: 60,
    startedAt: new Date(Date.now() - 60000).toISOString(),
    completedAt: new Date().toISOString(),
  };

  testLearningSession = await appwriteService.database.createLearningSession(sessionData);
  
  if (!testLearningSession || !testLearningSession.$id) {
    throw new Error('Learning session creation failed');
  }
  
  console.log(`   Learning session created: ${testLearningSession.$id}`);
}

async function testScanHistoryCreation() {
  const scanData = {
    userId: testUser.$id,
    originalImageId: 'test-image-id',
    extractedText: 'This is test extracted text from a scanned document.',
    scanType: 'document' as const,
    confidence: 0.95,
    language: 'en',
    metadata: {
      imageSize: 1024000,
      dimensions: { width: 800, height: 600 },
      processingTime: 2500,
    },
  };

  testScanRecord = await appwriteService.database.createScanRecord(scanData);
  
  if (!testScanRecord || !testScanRecord.$id) {
    throw new Error('Scan history creation failed');
  }
  
  console.log(`   Scan record created: ${testScanRecord.$id}`);
}

// Storage Tests
async function testFileUpload() {
  // Simulate file upload (we can't actually upload files in this test environment)
  // but we can test the service methods
  try {
    const buckets = ['user-avatars', 'scan-images', 'card-assets', 'user-exports'];
    
    for (const bucket of buckets) {
      // Test URL generation (this doesn't require actual files)
      const testUrl = appwriteService.storage.getFileUrl(bucket, 'test-file-id');
      if (!testUrl || !testUrl.includes(bucket)) {
        throw new Error(`File URL generation failed for bucket: ${bucket}`);
      }
    }
    
    console.log('   File URL generation working for all buckets');
  } catch (error) {
    throw new Error(`Storage test failed: ${error.message}`);
  }
}

// Security Tests
async function testPermissions() {
  // Test that user can only access their own data
  if (!testKnowledgeCard) {
    throw new Error('No test knowledge card available for permission testing');
  }

  // This should work (user accessing their own data)
  const ownCard = await appwriteService.database.getKnowledgeCard(testKnowledgeCard.$id);
  if (!ownCard) {
    throw new Error('User cannot access their own knowledge card');
  }

  console.log('   Permission validation passed');
}

// Cleanup
async function testCleanup() {
  try {
    // Delete test data
    if (testKnowledgeCard) {
      await appwriteService.database.deleteKnowledgeCard(testKnowledgeCard.$id);
      console.log('   Test knowledge card deleted');
    }

    // Sign out
    await appwriteService.auth.signOut();
    console.log('   User signed out');
    
    console.log('   Cleanup completed');
  } catch (error: any) {
    console.warn(`   Cleanup warning: ${error.message}`);
  }
}

// Results Summary
function printTestSummary() {
  console.log('\n📊 Test Results Summary');
  console.log('========================');
  
  const passed = testResults.filter(r => r.passed).length;
  const failed = testResults.filter(r => !r.passed).length;
  const total = testResults.length;
  
  console.log(`Total Tests: ${total}`);
  console.log(`Passed: ${passed} ✅`);
  console.log(`Failed: ${failed} ❌`);
  console.log(`Success Rate: ${((passed / total) * 100).toFixed(1)}%`);
  
  if (failed > 0) {
    console.log('\n❌ Failed Tests:');
    testResults
      .filter(r => !r.passed)
      .forEach(r => console.log(`   - ${r.name}: ${r.error}`));
  }
  
  const totalDuration = testResults.reduce((sum, r) => sum + r.duration, 0);
  console.log(`\nTotal Duration: ${totalDuration}ms`);
  
  if (failed === 0) {
    console.log('\n🎉 All tests passed! AppWrite integration is working correctly.');
  } else {
    console.log('\n⚠️  Some tests failed. Please check the AppWrite configuration.');
    process.exit(1);
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  runAppwriteTests().catch(console.error);
}

export { runAppwriteTests };
