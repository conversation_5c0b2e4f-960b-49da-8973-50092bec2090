import React, { useState } from 'react';
import { View } from 'react-native';
import { 
  <PERSON>dal, 
  ModalBackdrop, 
  Modal<PERSON>ontent, 
  ModalHeader, 
  ModalCloseButton, 
  ModalBody, 
  ModalFooter 
} from './components/ui/modal';
import { 
  AlertDialog, 
  AlertDialogBackdrop, 
  AlertDialogContent, 
  AlertDialogHeader, 
  AlertDialogCloseButton, 
  AlertDialogBody, 
  AlertDialogFooter 
} from './components/ui/alert-dialog';
import { 
  Popover, 
  PopoverBackdrop, 
  PopoverContent, 
  PopoverHeader, 
  PopoverBody, 
  PopoverFooter, 
  PopoverCloseButton 
} from './components/ui/popover';
import { Text } from './components/ui/text';
import { Button, ButtonText } from './components/ui/button';
import { VStack } from './components/ui/vstack';
import { HStack } from './components/ui/hstack';
import { XIcon, AlertTriangleIcon, InfoIcon, HelpCircleIcon } from 'lucide-react-native';

// Test component to validate enhanced Overlay Components
export function LearniScanOverlayTest() {
  const [showModal, setShowModal] = useState(false);
  const [showGlassModal, setShowGlassModal] = useState(false);
  const [showAlertDialog, setShowAlertDialog] = useState(false);
  const [showGlassAlert, setShowGlassAlert] = useState(false);
  const [showPopover, setShowPopover] = useState(false);
  const [showGlassPopover, setShowGlassPopover] = useState(false);

  return (
    <View style={{ padding: 20, backgroundColor: '#000' }}>
      <Text color="primary" style={{ marginBottom: 20, fontSize: 18 }}>
        Enhanced Overlay Components Test
      </Text>

      {/* Modal Triggers */}
      <Text color="secondary" style={{ marginBottom: 15, fontSize: 16 }}>
        Modal Components:
      </Text>
      
      <VStack space="md" style={{ marginBottom: 25 }}>
        <HStack space="md">
          <Button action="candyPink" onPress={() => setShowModal(true)}>
            <ButtonText>Standard Modal</ButtonText>
          </Button>
          
          <Button action="glass" onPress={() => setShowGlassModal(true)}>
            <ButtonText>Glass Modal</ButtonText>
          </Button>
        </HStack>
      </VStack>

      {/* Alert Dialog Triggers */}
      <Text color="secondary" style={{ marginBottom: 15, fontSize: 16 }}>
        Alert Dialog Components:
      </Text>
      
      <VStack space="md" style={{ marginBottom: 25 }}>
        <HStack space="md">
          <Button action="candyPurple" onPress={() => setShowAlertDialog(true)}>
            <ButtonText>Standard Alert</ButtonText>
          </Button>
          
          <Button action="glassCard" onPress={() => setShowGlassAlert(true)}>
            <ButtonText>Glass Alert</ButtonText>
          </Button>
        </HStack>
      </VStack>

      {/* Popover Triggers */}
      <Text color="secondary" style={{ marginBottom: 15, fontSize: 16 }}>
        Popover Components:
      </Text>
      
      <VStack space="md" style={{ marginBottom: 25 }}>
        <HStack space="md">
          <Popover
            isOpen={showPopover}
            onClose={() => setShowPopover(false)}
            trigger={(triggerProps) => (
              <Button 
                action="candyBlue" 
                {...triggerProps}
                onPress={() => setShowPopover(true)}
              >
                <ButtonText>Standard Popover</ButtonText>
              </Button>
            )}
          >
            <PopoverBackdrop />
            <PopoverContent variant="default" size="md">
              <PopoverHeader>
                <Text color="primary" size="lg" style={{ fontWeight: 'bold' }}>
                  LearniScan Info
                </Text>
                <PopoverCloseButton onPress={() => setShowPopover(false)}>
                  <XIcon size={16} color="#FF6B9D" />
                </PopoverCloseButton>
              </PopoverHeader>
              <PopoverBody>
                <Text color="secondary">
                  This is a standard popover with LearniScan content. 
                  Perfect for displaying additional information and quick actions.
                </Text>
              </PopoverBody>
              <PopoverFooter>
                <Button action="candyPink" size="sm" onPress={() => setShowPopover(false)}>
                  <ButtonText>Got it</ButtonText>
                </Button>
              </PopoverFooter>
            </PopoverContent>
          </Popover>
          
          <Popover
            isOpen={showGlassPopover}
            onClose={() => setShowGlassPopover(false)}
            trigger={(triggerProps) => (
              <Button 
                action="glass" 
                {...triggerProps}
                onPress={() => setShowGlassPopover(true)}
              >
                <ButtonText>Glass Popover</ButtonText>
              </Button>
            )}
          >
            <PopoverBackdrop />
            <PopoverContent variant="glass" size="md">
              <PopoverHeader>
                <Text color="primary" size="lg" style={{ fontWeight: 'bold' }}>
                  Glass Effect Info
                </Text>
                <PopoverCloseButton onPress={() => setShowGlassPopover(false)}>
                  <XIcon size={16} color="#FFFFFF" />
                </PopoverCloseButton>
              </PopoverHeader>
              <PopoverBody>
                <Text color="primary">
                  This popover features glass morphism effects with backdrop blur. 
                  Perfect for modern UI overlays in LearniScan.
                </Text>
              </PopoverBody>
              <PopoverFooter>
                <Button action="candyPurple" size="sm" onPress={() => setShowGlassPopover(false)}>
                  <ButtonText>Close</ButtonText>
                </Button>
              </PopoverFooter>
            </PopoverContent>
          </Popover>
        </HStack>
      </VStack>

      {/* Standard Modal */}
      <Modal isOpen={showModal} onClose={() => setShowModal(false)} size="md">
        <ModalBackdrop />
        <ModalContent variant="default">
          <ModalHeader>
            <Text color="primary" size="xl" style={{ fontWeight: 'bold' }}>
              Document Scan Settings
            </Text>
            <ModalCloseButton onPress={() => setShowModal(false)}>
              <XIcon size={20} color="#FF6B9D" />
            </ModalCloseButton>
          </ModalHeader>
          <ModalBody>
            <VStack space="md">
              <Text color="secondary">
                Configure your document scanning preferences for optimal results.
              </Text>
              
              <VStack space="sm">
                <Text color="tertiary" size="sm" style={{ fontWeight: '600' }}>
                  Scan Quality:
                </Text>
                <HStack space="sm">
                  <Button action="candyPink" size="sm" variant="outline">
                    <ButtonText>High</ButtonText>
                  </Button>
                  <Button action="candyPurple" size="sm">
                    <ButtonText>Ultra</ButtonText>
                  </Button>
                </HStack>
              </VStack>
              
              <VStack space="sm">
                <Text color="tertiary" size="sm" style={{ fontWeight: '600' }}>
                  Auto-Enhancement:
                </Text>
                <HStack space="sm">
                  <Button action="success" size="sm">
                    <ButtonText>Enabled</ButtonText>
                  </Button>
                  <Button action="default" size="sm" variant="outline">
                    <ButtonText>Disabled</ButtonText>
                  </Button>
                </HStack>
              </VStack>
            </VStack>
          </ModalBody>
          <ModalFooter>
            <HStack space="md">
              <Button action="default" variant="outline" onPress={() => setShowModal(false)}>
                <ButtonText>Cancel</ButtonText>
              </Button>
              <Button action="candyPink" onPress={() => setShowModal(false)}>
                <ButtonText>Save Settings</ButtonText>
              </Button>
            </HStack>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* Glass Modal */}
      <Modal isOpen={showGlassModal} onClose={() => setShowGlassModal(false)} size="lg">
        <ModalBackdrop variant="glass" />
        <ModalContent variant="glassCard">
          <ModalHeader>
            <HStack space="sm" style={{ alignItems: 'center' }}>
              <InfoIcon size={24} color="#A855F7" />
              <Text color="primary" size="xl" style={{ fontWeight: 'bold' }}>
                AI Processing Status
              </Text>
            </HStack>
            <ModalCloseButton onPress={() => setShowGlassModal(false)}>
              <XIcon size={20} color="#FFFFFF" />
            </ModalCloseButton>
          </ModalHeader>
          <ModalBody>
            <VStack space="lg">
              <Text color="primary">
                Your document is being processed using advanced AI algorithms 
                for optimal text recognition and enhancement.
              </Text>
              
              <VStack space="md">
                <HStack style={{ justifyContent: 'space-between' }}>
                  <Text color="secondary">Text Recognition</Text>
                  <Text color="success">Complete</Text>
                </HStack>
                <HStack style={{ justifyContent: 'space-between' }}>
                  <Text color="secondary">Image Enhancement</Text>
                  <Text color="candyPurple">Processing...</Text>
                </HStack>
                <HStack style={{ justifyContent: 'space-between' }}>
                  <Text color="secondary">Quality Analysis</Text>
                  <Text color="muted">Pending</Text>
                </HStack>
              </VStack>
            </VStack>
          </ModalBody>
          <ModalFooter>
            <HStack space="md">
              <Button action="glass" variant="outline" onPress={() => setShowGlassModal(false)}>
                <ButtonText>Run in Background</ButtonText>
              </Button>
              <Button action="candyPurple" onPress={() => setShowGlassModal(false)}>
                <ButtonText>View Progress</ButtonText>
              </Button>
            </HStack>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* Standard Alert Dialog */}
      <AlertDialog isOpen={showAlertDialog} onClose={() => setShowAlertDialog(false)}>
        <AlertDialogBackdrop />
        <AlertDialogContent variant="default">
          <AlertDialogHeader>
            <HStack space="sm" style={{ alignItems: 'center' }}>
              <AlertTriangleIcon size={24} color="#F59E0B" />
              <Text color="primary" size="lg" style={{ fontWeight: 'bold' }}>
                Delete Document?
              </Text>
            </HStack>
            <AlertDialogCloseButton onPress={() => setShowAlertDialog(false)}>
              <XIcon size={18} color="#FF6B9D" />
            </AlertDialogCloseButton>
          </AlertDialogHeader>
          <AlertDialogBody>
            <Text color="secondary">
              Are you sure you want to delete this scanned document? 
              This action cannot be undone.
            </Text>
          </AlertDialogBody>
          <AlertDialogFooter>
            <HStack space="md">
              <Button action="default" variant="outline" onPress={() => setShowAlertDialog(false)}>
                <ButtonText>Cancel</ButtonText>
              </Button>
              <Button action="negative" onPress={() => setShowAlertDialog(false)}>
                <ButtonText>Delete</ButtonText>
              </Button>
            </HStack>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Glass Alert Dialog */}
      <AlertDialog isOpen={showGlassAlert} onClose={() => setShowGlassAlert(false)}>
        <AlertDialogBackdrop variant="glassCard" />
        <AlertDialogContent variant="glass">
          <AlertDialogHeader>
            <HStack space="sm" style={{ alignItems: 'center' }}>
              <HelpCircleIcon size={24} color="#3B82F6" />
              <Text color="primary" size="lg" style={{ fontWeight: 'bold' }}>
                Upgrade to Premium?
              </Text>
            </HStack>
            <AlertDialogCloseButton onPress={() => setShowGlassAlert(false)}>
              <XIcon size={18} color="#FFFFFF" />
            </AlertDialogCloseButton>
          </AlertDialogHeader>
          <AlertDialogBody>
            <VStack space="md">
              <Text color="primary">
                Unlock advanced AI features, unlimited cloud storage, 
                and priority processing with LearniScan Premium.
              </Text>
              <VStack space="xs">
                <Text color="candyPink" size="sm">• Ultra HD scanning</Text>
                <Text color="candyPurple" size="sm">• Advanced OCR</Text>
                <Text color="candyBlue" size="sm">• Unlimited storage</Text>
              </VStack>
            </VStack>
          </AlertDialogBody>
          <AlertDialogFooter>
            <HStack space="md">
              <Button action="glass" variant="outline" onPress={() => setShowGlassAlert(false)}>
                <ButtonText>Maybe Later</ButtonText>
              </Button>
              <Button action="candyPink" onPress={() => setShowGlassAlert(false)}>
                <ButtonText>Upgrade Now</ButtonText>
              </Button>
            </HStack>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </View>
  );
}