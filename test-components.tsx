import React from 'react';
import { View } from 'react-native';
import { Button, ButtonText } from './components/ui/button';
import { Text } from './components/ui/text';
import { Input, InputField } from './components/ui/input';
import { Box } from './components/ui/box';

// Test component to validate LearniScan design system integration
export function LearniScanTestComponents() {
  return (
    <View style={{ padding: 20, backgroundColor: '#000' }}>
      {/* Test Candy Color Buttons */}
      <Text color="primary" style={{ marginBottom: 10 }}>Candy Color Buttons:</Text>
      
      <Button action="candyPink" style={{ marginBottom: 8 }}>
        <ButtonText>Candy Pink Button</ButtonText>
      </Button>
      
      <Button action="candyPurple" style={{ marginBottom: 8 }}>
        <ButtonText>Candy Purple Button</ButtonText>
      </Button>
      
      <Button action="candyBlue" style={{ marginBottom: 8 }}>
        <ButtonText>Candy Blue Button</ButtonText>
      </Button>

      {/* Test Glass Effect Buttons */}
      <Text color="primary" style={{ marginBottom: 10, marginTop: 20 }}>Glass Effect Buttons:</Text>
      
      <Button action="glass" style={{ marginBottom: 8 }}>
        <ButtonText>Glass Button</ButtonText>
      </Button>
      
      <Button action="glassCard" style={{ marginBottom: 8 }}>
        <ButtonText>Glass Card Button</ButtonText>
      </Button>

      {/* Test Text Color Variants */}
      <Text color="primary" style={{ marginBottom: 10, marginTop: 20 }}>Text Color Variants:</Text>
      <Text color="primary">Primary Text (100% white)</Text>
      <Text color="secondary">Secondary Text (80% white)</Text>
      <Text color="tertiary">Tertiary Text (70% white)</Text>
      <Text color="muted">Muted Text (60% white)</Text>
      <Text color="candyPink">Candy Pink Accent Text</Text>
      <Text color="candyPurple">Candy Purple Accent Text</Text>
      <Text color="candyBlue">Candy Blue Accent Text</Text>

      {/* Test Glass Input Variants */}
      <Text color="primary" style={{ marginBottom: 10, marginTop: 20 }}>Glass Input Variants:</Text>
      
      <Input variant="glass" style={{ marginBottom: 8 }}>
        <InputField placeholder="Glass input field..." />
      </Input>
      
      <Input variant="glassRounded" style={{ marginBottom: 8 }}>
        <InputField placeholder="Glass rounded input..." />
      </Input>

      {/* Test Box Glass Variants */}
      <Text color="primary" style={{ marginBottom: 10, marginTop: 20 }}>Glass Box Variants:</Text>
      
      <Box variant="glass" style={{ padding: 16, marginBottom: 8 }}>
        <Text color="primary">Glass Box Container</Text>
      </Box>
      
      <Box variant="glassCard" style={{ marginBottom: 8 }}>
        <Text color="primary">Glass Card Container (with padding)</Text>
      </Box>
      
      <Box variant="candyBorder" style={{ padding: 16 }}>
        <Text color="primary">Candy Border Box</Text>
      </Box>
    </View>
  );
}