import React, { useState } from 'react';
import { View, ScrollView } from 'react-native';
import { DocumentScanner } from './components/learni-scan/DocumentScanner';
import { ScanPreview } from './components/learni-scan/ScanPreview';
import { QualityIndicator } from './components/learni-scan/QualityIndicator';
import { ScanProgress } from './components/learni-scan/ScanProgress';
import { Text } from './components/ui/text';
import { Button, ButtonText } from './components/ui/button';
import { VStack } from './components/ui/vstack';
import { HStack } from './components/ui/hstack';
import { Box } from './components/ui/box';
import { Divider } from './components/ui/divider';
import { 
  CameraIcon, 
  EyeIcon, 
  ZapIcon,
  SparklesIcon,
  FileTextIcon,
  ImageIcon
} from 'lucide-react-native';

// Test component to validate custom LearniScan components
export function LearniScanComponentsTest() {
  const [currentDemo, setCurrentDemo] = useState<'overview' | 'scanner' | 'preview' | 'quality' | 'progress'>('overview');
  const [scanProgress, setScanProgress] = useState(0);
  const [isScanning, setIsScanning] = useState(false);

  // Mock image URI for testing
  const mockImageUri = 'https://via.placeholder.com/400x600/1a1a1a/ffffff?text=Document+Scan';

  const qualityFactors = [
    {
      name: 'Lighting',
      score: 85,
      icon: ZapIcon,
      description: 'Document lighting and visibility'
    },
    {
      name: 'Focus',
      score: 92,
      icon: EyeIcon,
      description: 'Image sharpness and clarity'
    },
    {
      name: 'Angle',
      score: 78,
      icon: SparklesIcon,
      description: 'Document positioning and perspective'
    },
    {
      name: 'Text Quality',
      score: 88,
      icon: FileTextIcon,
      description: 'Text readability and contrast'
    }
  ];

  const startScanProgress = () => {
    setIsScanning(true);
    setScanProgress(0);
    
    const interval = setInterval(() => {
      setScanProgress(prev => {
        if (prev >= 4) {
          clearInterval(interval);
          setIsScanning(false);
          return 4;
        }
        return prev + 1;
      });
    }, 2000);
  };

  const renderOverview = () => (
    <ScrollView className="flex-1 bg-black">
      <VStack space="lg" className="p-6">
        <VStack space="md" style={{ alignItems: 'center' }}>
          <Text color="primary" size="2xl" style={{ fontWeight: 'bold', textAlign: 'center' }}>
            LearniScan Custom Components
          </Text>
          <Text color="secondary" size="md" style={{ textAlign: 'center' }}>
            Phase 3: Document scanning and AI processing components with candy colors and glass effects
          </Text>
        </VStack>

        <Divider variant="candyPink" orientation="horizontal" />

        {/* Component Showcase Grid */}
        <VStack space="lg">
          {/* DocumentScanner Demo */}
          <Box className="bg-glass-bg-primary border border-glass-border-primary rounded-xl p-6">
            <VStack space="md">
              <HStack className="items-center space-x-3">
                <Box className="w-12 h-12 justify-center items-center rounded-full bg-candyPink/20">
                  <CameraIcon size={24} color="#FF6B9D" />
                </Box>
                <VStack space="xs" className="flex-1">
                  <Text color="primary" size="lg" style={{ fontWeight: 'bold' }}>
                    DocumentScanner
                  </Text>
                  <Text color="secondary" size="sm">
                    Camera interface with glass overlay and candy color controls
                  </Text>
                </VStack>
              </HStack>
              
              <VStack space="sm">
                <Text color="tertiary" size="sm">Features:</Text>
                <VStack space="xs">
                  <Text color="secondary" size="xs">• Glass morphism camera overlay</Text>
                  <Text color="secondary" size="xs">• Candy color document frame guide</Text>
                  <Text color="secondary" size="xs">• Enhanced flash and camera controls</Text>
                  <Text color="secondary" size="xs">• Quality settings and permissions handling</Text>
                </VStack>
              </VStack>
              
              <Button action="candyPink" onPress={() => setCurrentDemo('scanner')}>
                <ButtonText>View DocumentScanner</ButtonText>
              </Button>
            </VStack>
          </Box>

          {/* ScanPreview Demo */}
          <Box className="bg-glass-bg-primary border border-glass-border-primary rounded-xl p-6">
            <VStack space="md">
              <HStack className="items-center space-x-3">
                <Box className="w-12 h-12 justify-center items-center rounded-full bg-candyPurple/20">
                  <ImageIcon size={24} color="#A855F7" />
                </Box>
                <VStack space="xs" className="flex-1">
                  <Text color="primary" size="lg" style={{ fontWeight: 'bold' }}>
                    ScanPreview
                  </Text>
                  <Text color="secondary" size="sm">
                    Document preview with enhancement controls and glass effects
                  </Text>
                </VStack>
              </HStack>
              
              <VStack space="sm">
                <Text color="tertiary" size="sm">Features:</Text>
                <VStack space="xs">
                  <Text color="secondary" size="xs">• Interactive enhancement sliders</Text>
                  <Text color="secondary" size="xs">• Document mode selection</Text>
                  <Text color="secondary" size="xs">• AI processing simulation</Text>
                  <Text color="secondary" size="xs">• Glass morphism enhancement panel</Text>
                </VStack>
              </VStack>
              
              <Button action="candyPurple" onPress={() => setCurrentDemo('preview')}>
                <ButtonText>View ScanPreview</ButtonText>
              </Button>
            </VStack>
          </Box>

          {/* QualityIndicator Demo */}
          <Box className="bg-glass-bg-primary border border-glass-border-primary rounded-xl p-6">
            <VStack space="md">
              <HStack className="items-center space-x-3">
                <Box className="w-12 h-12 justify-center items-center rounded-full bg-candyBlue/20">
                  <EyeIcon size={24} color="#3B82F6" />
                </Box>
                <VStack space="xs" className="flex-1">
                  <Text color="primary" size="lg" style={{ fontWeight: 'bold' }}>
                    QualityIndicator
                  </Text>
                  <Text color="secondary" size="sm">
                    Real-time scan quality feedback with candy color states
                  </Text>
                </VStack>
              </HStack>
              
              <VStack space="sm">
                <Text color="tertiary" size="sm">Features:</Text>
                <VStack space="xs">
                  <Text color="secondary" size="xs">• Animated quality scoring</Text>
                  <Text color="secondary" size="xs">• Detailed factor breakdown</Text>
                  <Text color="secondary" size="xs">• Improvement suggestions</Text>
                  <Text color="secondary" size="xs">• Candy color quality levels</Text>
                </VStack>
              </VStack>
              
              <Button action="candyBlue" onPress={() => setCurrentDemo('quality')}>
                <ButtonText>View QualityIndicator</ButtonText>
              </Button>
            </VStack>
          </Box>

          {/* ScanProgress Demo */}
          <Box className="bg-glass-bg-primary border border-glass-border-primary rounded-xl p-6">
            <VStack space="md">
              <HStack className="items-center space-x-3">
                <Box className="w-12 h-12 justify-center items-center rounded-full bg-success/20">
                  <SparklesIcon size={24} color="#10B981" />
                </Box>
                <VStack space="xs" className="flex-1">
                  <Text color="primary" size="lg" style={{ fontWeight: 'bold' }}>
                    ScanProgress
                  </Text>
                  <Text color="secondary" size="sm">
                    AI processing progress with animated candy color indicators
                  </Text>
                </VStack>
              </HStack>
              
              <VStack space="sm">
                <Text color="tertiary" size="sm">Features:</Text>
                <VStack space="xs">
                  <Text color="secondary" size="xs">• Animated progress ring</Text>
                  <Text color="secondary" size="xs">• Step-by-step processing display</Text>
                  <Text color="secondary" size="xs">• Estimated time indicators</Text>
                  <Text color="secondary" size="xs">• Completion celebrations</Text>
                </VStack>
              </VStack>
              
              <Button action="success" onPress={() => setCurrentDemo('progress')}>
                <ButtonText>View ScanProgress</ButtonText>
              </Button>
            </VStack>
          </Box>
        </VStack>

        <Divider variant="glass" orientation="horizontal" />

        {/* Live Quality Demo */}
        <VStack space="md">
          <Text color="tertiary" size="lg" style={{ fontWeight: 'bold', textAlign: 'center' }}>
            Live Quality Demo
          </Text>
          
          <QualityIndicator 
            score={87}
            factors={qualityFactors}
            showDetails={true}
            size="md"
            animated={true}
          />
        </VStack>

        <Divider variant="glass" orientation="horizontal" />

        {/* Live Progress Demo */}
        <VStack space="md">
          <Text color="tertiary" size="lg" style={{ fontWeight: 'bold', textAlign: 'center' }}>
            Live Progress Demo
          </Text>
          
          <ScanProgress 
            currentStep={scanProgress}
            showDetails={true}
            animated={true}
          />
          
          <HStack className="justify-center">
            <Button 
              action="candyPink" 
              onPress={startScanProgress}
              disabled={isScanning}
            >
              <ButtonText>
                {isScanning ? 'Processing...' : 'Start Demo Processing'}
              </ButtonText>
            </Button>
          </HStack>
        </VStack>
      </VStack>
    </ScrollView>
  );

  const renderDemo = () => {
    switch (currentDemo) {
      case 'scanner':
        return (
          <DocumentScanner
            onCapture={(uri) => {
              console.log('Captured:', uri);
              setCurrentDemo('overview');
            }}
            onClose={() => setCurrentDemo('overview')}
            onSettings={() => console.log('Settings')}
            quality="high"
          />
        );
        
      case 'preview':
        return (
          <ScanPreview
            imageUri={mockImageUri}
            onAccept={(uri, settings) => {
              console.log('Accepted:', uri, settings);
              setCurrentDemo('overview');
            }}
            onRetake={() => setCurrentDemo('scanner')}
            onCancel={() => setCurrentDemo('overview')}
          />
        );
        
      case 'quality':
        return (
          <View className="flex-1 bg-black justify-center items-center p-6">
            <VStack space="lg" style={{ alignItems: 'center' }}>
              <Button action="glass" variant="outline" onPress={() => setCurrentDemo('overview')}>
                <ButtonText>← Back to Overview</ButtonText>
              </Button>
              
              <Text color="primary" size="xl" style={{ fontWeight: 'bold' }}>
                Quality Indicator Demo
              </Text>
              
              <QualityIndicator 
                score={87}
                factors={qualityFactors}
                showDetails={true}
                size="lg"
                animated={true}
              />
              
              <VStack space="md">
                <Text color="tertiary" size="md" style={{ fontWeight: '600', textAlign: 'center' }}>
                  Different Quality Levels:
                </Text>
                
                <HStack className="justify-between w-full">
                  <QualityIndicator score={95} size="sm" />
                  <QualityIndicator score={75} size="sm" />
                  <QualityIndicator score={45} size="sm" />
                  <QualityIndicator score={25} size="sm" />
                </HStack>
              </VStack>
            </VStack>
          </View>
        );
        
      case 'progress':
        return (
          <View className="flex-1 bg-black justify-center items-center p-6">
            <VStack space="lg" style={{ alignItems: 'center' }}>
              <Button action="glass" variant="outline" onPress={() => setCurrentDemo('overview')}>
                <ButtonText>← Back to Overview</ButtonText>
              </Button>
              
              <Text color="primary" size="xl" style={{ fontWeight: 'bold' }}>
                Scan Progress Demo
              </Text>
              
              <ScanProgress 
                currentStep={scanProgress}
                showDetails={true}
                animated={true}
                onComplete={() => console.log('Processing complete!')}
              />
              
              <HStack className="space-x-4">
                <Button 
                  action="candyPink" 
                  size="sm"
                  onPress={startScanProgress}
                  disabled={isScanning}
                >
                  <ButtonText>Start Processing</ButtonText>
                </Button>
                
                <Button 
                  action="glass" 
                  variant="outline"
                  size="sm"
                  onPress={() => setScanProgress(0)}
                >
                  <ButtonText>Reset</ButtonText>
                </Button>
              </HStack>
            </VStack>
          </View>
        );
        
      default:
        return renderOverview();
    }
  };

  return renderDemo();
}

export default LearniScanComponentsTest;